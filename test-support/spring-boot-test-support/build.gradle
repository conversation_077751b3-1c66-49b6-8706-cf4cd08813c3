/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java-library"
	id "org.springframework.boot.optional-dependencies"
}

description = "Spring Boot Testing Support"

dependencies {
	api(platform(project(path: ":platform:spring-boot-internal-dependencies")))
	api("com.jayway.jsonpath:json-path")
	api("org.assertj:assertj-core")
	api("org.awaitility:awaitility")
	api("org.hamcrest:hamcrest-core")
	api("org.hamcrest:hamcrest-library")
	api("org.junit.jupiter:junit-jupiter")
	api("org.mockito:mockito-core")
	api("org.mockito:mockito-junit-jupiter")
	api("org.skyscreamer:jsonassert")
	api("org.springframework:spring-core")
	api("org.springframework:spring-test")
	api("org.springframework:spring-core-test")

	compileOnly("org.apache.cassandra:java-driver-core") {
		exclude(group: "org.slf4j", module: "jcl-over-slf4j")
	}
	compileOnly("jakarta.servlet:jakarta.servlet-api")
	compileOnly("junit:junit")
	compileOnly("org.junit.platform:junit-platform-engine")
	compileOnly("org.junit.platform:junit-platform-launcher")
	compileOnly("org.springframework:spring-context")
	compileOnly("org.springframework.data:spring-data-redis")

	implementation("jakarta.inject:jakarta.inject-api")
	implementation("org.apache.maven.resolver:maven-resolver-connector-basic")
	implementation("org.apache.maven.resolver:maven-resolver-impl")
	implementation("org.apache.maven:maven-resolver-provider") {
		exclude(group: "javax.inject", module: "javax.inject")
	}
	implementation("org.apache.maven.resolver:maven-resolver-transport-http") {
		exclude group: "org.slf4j", module: "jcl-over-slf4j"
	}

	testImplementation("jakarta.servlet:jakarta.servlet-api")
	testImplementation("org.springframework:spring-context")

	testRuntimeOnly("org.hibernate.validator:hibernate-validator")
}

architectureCheck {
	nullMarked = false
}
