/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java-library"
	id "org.springframework.boot.optional-dependencies"
}

description = "Spring Boot Docker Testing Support"

dependencies {
	api(platform(project(path: ":platform:spring-boot-internal-dependencies")))
	api(project(path: ":test-support:spring-boot-test-support"))

	compileOnly("org.testcontainers:testcontainers")

	compileOnly("org.junit.jupiter:junit-jupiter")
	compileOnly("org.springframework:spring-core")

	optional("org.testcontainers:activemq")
	optional("org.testcontainers:cassandra")
	optional("org.testcontainers:couchbase")
	optional("org.testcontainers:elasticsearch")
	optional("org.testcontainers:grafana")
	optional("org.testcontainers:junit-jupiter")
	optional("org.testcontainers:kafka")
	optional("org.testcontainers:ldap")
	optional("org.testcontainers:mongodb")
	optional("org.testcontainers:neo4j")
	optional("org.testcontainers:oracle-xe")
	optional("org.testcontainers:oracle-free")
	optional("org.testcontainers:postgresql")
	optional("org.testcontainers:pulsar")
	optional("org.testcontainers:rabbitmq")
	optional("org.testcontainers:redpanda")
	optional("com.redis:testcontainers-redis")
}

architectureCheck {
	nullMarked = false
}
