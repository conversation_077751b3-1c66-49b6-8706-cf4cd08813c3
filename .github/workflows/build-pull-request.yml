name: Build Pull Request
on: pull_request
permissions:
  contents: read
jobs:
  build:
    name: Build Pull Request
    if: ${{ github.repository == 'spring-projects/spring-boot' }}
    runs-on: ${{ vars.UBUNTU_MEDIUM || 'ubuntu-latest' }}
    steps:
      - name: Check Out Code
        uses: actions/checkout@v5
      - name: Build
        id: build
        uses: ./.github/actions/build
      - name: Print JVM Thread Dumps When Cancelled
        if: cancelled()
        uses: ./.github/actions/print-jvm-thread-dumps
      - name: Upload Build Reports
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: build-reports
          path: '**/build/reports/'
