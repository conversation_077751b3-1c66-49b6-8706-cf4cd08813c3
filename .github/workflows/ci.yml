name: CI
on:
  push:
    branches:
      - 'main'
permissions:
  contents: read
jobs:
  ci:
    name: '${{ matrix.os.name}} | Java ${{ matrix.java.version}}'
    if: ${{ github.repository == 'spring-projects/spring-boot' || github.repository == 'spring-projects/spring-boot-commercial' }}
    runs-on: ${{ matrix.os.id }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - id: ${{ vars.UBUNTU_MEDIUM || 'ubuntu-latest' }}
            name: Linux
          - id: windows-latest
            name: Windows
        java:
          - version: 17
            toolchain: true
          - version: 21
            toolchain: true
          - version: 24
            toolchain: false
        exclude:
          - os:
              name: Linux
            java:
              version: 24
          - os:
              name: ${{ github.repository == 'spring-projects/spring-boot-commercial' && 'Windows' }}
    steps:
      - name: Prepare Windows runner
        if: ${{ runner.os == 'Windows' }}
        run: |
          git config --global core.autocrlf true
          git config --global core.longPaths true
          Stop-Service -name Docker
      - name: Check Out Code
        uses: actions/checkout@v5
      - name: Build
        id: build
        uses: ./.github/actions/build
        with:
          commercial-release-repository-url: ${{ vars.COMMERCIAL_RELEASE_REPO_URL }}
          commercial-repository-password: ${{ secrets.COMMERCIAL_ARTIFACTORY_RO_PASSWORD }}
          commercial-repository-username: ${{ secrets.COMMERCIAL_ARTIFACTORY_RO_USERNAME }}
          commercial-snapshot-repository-url: ${{ vars.COMMERCIAL_SNAPSHOT_REPO_URL }}
          develocity-access-key: ${{ secrets.DEVELOCITY_ACCESS_KEY }}
          gradle-cache-read-only: false
          java-early-access: ${{ matrix.java.early-access || 'false' }}
          java-distribution: ${{ matrix.java.distribution }}
          java-toolchain: ${{ matrix.java.toolchain }}
          java-version: ${{ matrix.java.version }}
      - name: Send Notification
        if: always()
        uses: ./.github/actions/send-notification
        with:
          build-scan-url: ${{ steps.build.outputs.build-scan-url }}
          run-name: ${{ format('{0} | {1} | Java {2}', github.ref_name, matrix.os.name, matrix.java.version) }}
          status: ${{ job.status }}
          webhook-url: ${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}
