name: Run System Tests
on:
  push:
    branches:
      - 'main'
permissions:
  contents: read
jobs:
  run-system-tests:
    name: 'Java ${{ matrix.java.version}}'
    if: ${{ github.repository == 'spring-projects/spring-boot' }}
    runs-on: ${{ vars.UBUNTU_MEDIUM || 'ubuntu-latest' }}
    strategy:
      matrix:
        java:
          - version: 17
            toolchain: true
          - version: 21
            toolchain: true
    steps:
      - name: Check Out Code
        uses: actions/checkout@v5
      - name: Prepare Gradle Build
        uses: ./.github/actions/prepare-gradle-build
        with:
          develocity-access-key: ${{ secrets.DEVELOCITY_ACCESS_KEY }}
          java-toolchain: ${{ matrix.java.toolchain }}
          java-version: ${{ matrix.java.version }}
      - name: Run System Tests
        id: run-system-tests
        shell: bash
        run: ./gradlew systemTest
      - name: Send Notification
        if: always()
        uses: ./.github/actions/send-notification
        with:
          build-scan-url: ${{ steps.run-system-tests.outputs.build-scan-url }}
          run-name: ${{ format('{0} | System Tests | Java {1}', github.ref_name, matrix.java.version) }}
          status: ${{ job.status }}
          webhook-url: ${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}
