name: Sync to <PERSON>ven <PERSON>
description: 'Syncs a release to Maven <PERSON> and waits for it to be available for use'
inputs:
  central-token-password:
    description: 'Password for authentication with central.sonatype.com'
    required: true
  central-token-username:
    description: 'Username for authentication with central.sonatype.com'
    required: true
  jfrog-cli-config-token:
    description: 'Config token for the JFrog CLI'
    required: true
  spring-boot-version:
    description: 'Version of Spring Boot that is being synced to Central'
    required: true
runs:
  using: composite
  steps:
    - name: Set Up JFrog CLI
      uses: jfrog/setup-jfrog-cli@2bc6e55719cd37dfb2f655a1e4f16c3440182a9f # v4.5.13
      env:
        JF_ENV_SPRING: ${{ inputs.jfrog-cli-config-token }}
    - name: Download Release Artifacts
      shell: bash
      run: jf rt download --spec ${{ format('{0}/artifacts.spec', github.action_path) }} --spec-vars 'buildName=${{ format('spring-boot-{0}', inputs.spring-boot-version) }};buildNumber=${{ github.run_number }}'
    - name: Sync
      uses: spring-io/central-publish-action@0cdd90d12e6876341e82860d951e1bcddc1e51b6 # v0.2.0
      with:
        token: ${{ inputs.central-token-password }}
        token-name: ${{ inputs.central-token-username }}
    - name: Await
      uses: ./.github/actions/await-http-resource
      with:
        url: ${{ format('https://repo.maven.apache.org/maven2/org/springframework/boot/spring-boot/{0}/spring-boot-{0}.jar', inputs.spring-boot-version) }}
