name: Prepare Gradle Build
description: 'Prepares a Gradle build. Sets up Java and Gradle and configures Gradle properties'
inputs:
  cache-read-only:
    description: 'Whether Gradle''s cache should be read only'
    required: false
    default: 'true'
  develocity-access-key:
    description: 'Access key for authentication with ge.spring.io'
    required: false
  java-distribution:
    description: 'Java distribution to use'
    required: false
    default: 'liberica'
  java-early-access:
    description: 'Whether the Java version is in early access. When true, forces java-distribution to temurin'
    required: false
    default: 'false'
  java-toolchain:
    description: 'Whether a Java toolchain should be used'
    required: false
    default: 'false'
  java-version:
    description: 'Java version to use for the build'
    required: false
    default: '24'
runs:
  using: composite
  steps:
    - name: Free Disk Space
      if: ${{ runner.os == 'Linux' }}
      uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
      with:
        tool-cache: true
        docker-images: false
    - name: Set Up Java
      uses: actions/setup-java@v4
      with:
        distribution: ${{ inputs.java-early-access == 'true' && 'temurin' || (inputs.java-distribution || 'liberica') }}
        java-version: |
          ${{ inputs.java-early-access == 'true' && format('{0}-ea', inputs.java-version) || inputs.java-version }}
          ${{ inputs.java-toolchain == 'true' && '24' || '' }}
    - name: Set Up Gradle With Read/Write Cache
      if: ${{ inputs.cache-read-only == 'false' }}
      uses: gradle/actions/setup-gradle@017a9effdb900e5b5b2fddfb590a105619dca3c3 # v4.4.2
      with:
        cache-read-only: false
        develocity-access-key: ${{ inputs.develocity-access-key }}
    - name: Set Up Gradle
      uses: gradle/actions/setup-gradle@017a9effdb900e5b5b2fddfb590a105619dca3c3 # v4.4.2
      with:
        develocity-access-key: ${{ inputs.develocity-access-key }}
        develocity-token-expiry: 4
    - name: Configure Gradle Properties
      shell: bash
      run: |
        mkdir -p $HOME/.gradle
        echo 'systemProp.user.name=spring-builds+github' >> $HOME/.gradle/gradle.properties
        echo 'systemProp.org.gradle.internal.launcher.welcomeMessageEnabled=false' >> $HOME/.gradle/gradle.properties
        echo 'org.gradle.daemon=false' >> $HOME/.gradle/gradle.properties
    - name: Configure Toolchain Properties
      if: ${{ inputs.java-toolchain == 'true' }}
      shell: bash
      run: |
        echo toolchainVersion=${{ inputs.java-version }} >> $HOME/.gradle/gradle.properties
        echo systemProp.org.gradle.java.installations.auto-detect=false >> $HOME/.gradle/gradle.properties
        echo systemProp.org.gradle.java.installations.auto-download=false >> $HOME/.gradle/gradle.properties
        echo systemProp.org.gradle.java.installations.paths=${{ format('$JAVA_HOME_{0}_X64', inputs.java-version) }} >> $HOME/.gradle/gradle.properties
