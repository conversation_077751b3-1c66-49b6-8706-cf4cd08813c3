/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java-library"
	id "org.springframework.boot.deployed"
}

description = "Spring Boot Classic Loader"

dependencies {
	compileOnly("org.springframework:spring-core")

	testImplementation(project(":test-support:spring-boot-test-support"))

	testRuntimeOnly("ch.qos.logback:logback-classic")
	testRuntimeOnly("org.bouncycastle:bcprov-jdk18on:1.78.1")
	testRuntimeOnly("org.springframework:spring-webmvc")
}

tasks.configureEach {
	if ("checkArchitectureMain".equals(it.name)) {
		prohibitObjectsRequireNonNull = false
	}
}

architectureCheck {
	nullMarked = false
}
