/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java"
}

description = "Spring Boot Configuration Metadata Changelog Generator"

configurations {
	oldMetadata {
		transitive = false
	}
	newMetadata {
		transitive = false
	}
}

dependencies {
	implementation(enforcedPlatform(project(":platform:spring-boot-dependencies")))
	implementation(project(":configuration-metadata:spring-boot-configuration-metadata"))

	testImplementation(enforcedPlatform(project(":platform:spring-boot-dependencies")))
	testImplementation("org.assertj:assertj-core")
	testImplementation("org.junit.jupiter:junit-jupiter")
}

architectureCheck {
	nullMarked = false
}

def dependenciesOf(String version) {
	if (version.startsWith("4.")) {
		return [
			"spring-boot",
			"spring-boot-activemq",
			"spring-boot-actuator",
			"spring-boot-actuator-autoconfigure",
			"spring-boot-amqp",
			"spring-boot-artemis",
			"spring-boot-autoconfigure",
			"spring-boot-batch",
			"spring-boot-cache",
			"spring-boot-cassandra",
			"spring-boot-couchbase",
			"spring-boot-data-cassandra",
			"spring-boot-data-commons",
			"spring-boot-data-couchbase",
			"spring-boot-data-elasticsearch",
			"spring-boot-data-jdbc",
			"spring-boot-data-jpa",
			"spring-boot-data-ldap",
			"spring-boot-data-mongodb",
			"spring-boot-data-neo4j",
			"spring-boot-data-r2dbc",
			"spring-boot-data-redis",
			"spring-boot-data-rest",
			"spring-boot-devtools",
			"spring-boot-docker-compose",
			"spring-boot-elasticsearch",
			"spring-boot-flyway",
			"spring-boot-freemarker",
			"spring-boot-graphql",
			"spring-boot-groovy-templates",
			"spring-boot-gson",
			"spring-boot-h2console",
			"spring-boot-hateoas",
			"spring-boot-hazelcast",
			"spring-boot-hibernate",
			"spring-boot-http-client",
			"spring-boot-http-codec",
			"spring-boot-http-converter",
			"spring-boot-integration",
			"spring-boot-jackson",
			"spring-boot-jdbc",
			"spring-boot-jersey",
			"spring-boot-jetty",
			"spring-boot-jms",
			"spring-boot-jooq",
			"spring-boot-jpa",
			"spring-boot-kafka",
			"spring-boot-ldap",
			"spring-boot-liquibase",
			"spring-boot-mail",
			"spring-boot-metrics",
			"spring-boot-mongodb",
			"spring-boot-mustache",
			"spring-boot-neo4j",
			"spring-boot-netty",
			"spring-boot-observation",
			"spring-boot-opentelemetry",
			"spring-boot-pulsar",
			"spring-boot-quartz",
			"spring-boot-r2dbc",
			"spring-boot-reactor",
			"spring-boot-reactor-netty",
			"spring-boot-restclient",
			"spring-boot-rsocket",
			"spring-boot-security",
			"spring-boot-security-oauth2-authorization-server",
			"spring-boot-security-oauth2-client",
			"spring-boot-security-oauth2-resource-server",
			"spring-boot-security-saml2",
			"spring-boot-sendgrid",
			"spring-boot-servlet",
			"spring-boot-session",
			"spring-boot-session-data-mongodb",
			"spring-boot-session-data-redis",
			"spring-boot-session-hazelcast",
			"spring-boot-session-jdbc",
			"spring-boot-sql",
			"spring-boot-test-autoconfigure",
			"spring-boot-testcontainers",
			"spring-boot-thymeleaf",
			"spring-boot-tomcat",
			"spring-boot-tracing",
			"spring-boot-tx",
			"spring-boot-undertow",
			"spring-boot-validation",
			"spring-boot-web-server",
			"spring-boot-webclient",
			"spring-boot-webflux",
			"spring-boot-webmvc",
			"spring-boot-webservices",
			"spring-boot-zipkin"
		]
	}
	return [
		"spring-boot",
		"spring-boot-actuator",
		"spring-boot-actuator-autoconfigure",
		"spring-boot-autoconfigure",
		"spring-boot-devtools",
		"spring-boot-test-autoconfigure"
	]
}

if (project.hasProperty("oldVersion") && project.hasProperty("newVersion")) {
	dependencies {
		dependenciesOf(oldVersion).each {
			oldMetadata("org.springframework.boot:$it:$oldVersion")
		}
		dependenciesOf(newVersion).each {
			newMetadata("org.springframework.boot:$it:$newVersion")
		}
	}

	def prepareOldMetadata = tasks.register("prepareOldMetadata", Sync) {
		from(configurations.oldMetadata)
		if (project.hasProperty("oldVersion")) {
			destinationDir = project.file("build/configuration-metadata-diff/$oldVersion")
		}
	}

	def prepareNewMetadata = tasks.register("prepareNewMetadata", Sync) {
		from(configurations.newMetadata)
		if (project.hasProperty("newVersion")) {
			destinationDir = project.file("build/configuration-metadata-diff/$newVersion")
		}
	}

	tasks.register("generate", JavaExec) {
		inputs.files(prepareOldMetadata, prepareNewMetadata)
		outputs.file(project.file("build/configuration-metadata-changelog.adoc"))
		classpath = sourceSets.main.runtimeClasspath
		mainClass = 'org.springframework.boot.configurationmetadata.changelog.ChangelogGenerator'
		if (project.hasProperty("oldVersion") && project.hasProperty("newVersion")) {
			args = [project.file("build/configuration-metadata-diff/$oldVersion"), project.file("build/configuration-metadata-diff/$newVersion"), project.file("build/configuration-metadata-changelog.adoc")]
		}
	}
}
