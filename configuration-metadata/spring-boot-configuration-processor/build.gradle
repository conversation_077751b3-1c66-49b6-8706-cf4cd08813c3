/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java-library"
	id "org.springframework.boot.deployed"
	id "org.springframework.boot.annotation-processor"
}

description = "Spring Boot Configuration Annotation Processor"

sourceSets {
	main {
		java {
			srcDir file("src/json-shade/java")
		}
	}
}

architectureCheck {
	nullMarked = false
}

dependencies {
	testCompileOnly("com.google.code.findbugs:jsr305:3.0.2")

	testImplementation(enforcedPlatform(project(":platform:spring-boot-dependencies")))
	testImplementation(project(":test-support:spring-boot-test-support"))
	testImplementation("jakarta.validation:jakarta.validation-api")
	testImplementation("org.projectlombok:lombok")
	testImplementation("org.apache.commons:commons-dbcp2")
}
