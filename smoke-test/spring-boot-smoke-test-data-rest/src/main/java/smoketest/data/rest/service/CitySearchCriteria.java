/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package smoketest.data.rest.service;

import java.io.Serializable;

import org.springframework.util.Assert;

public class CitySearchCriteria implements Serializable {

	private static final long serialVersionUID = 1L;

	private final String name;

	public CitySearchCriteria(String name) {
		Assert.notNull(name, "'name' must not be null");
		this.name = name;
	}

	public String getName() {
		return this.name;
	}

}
