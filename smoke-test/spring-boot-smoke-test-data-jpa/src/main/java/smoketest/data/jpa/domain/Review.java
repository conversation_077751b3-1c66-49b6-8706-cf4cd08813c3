/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package smoketest.data.jpa.domain;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;

import org.springframework.util.Assert;

@Entity
public class Review implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = "review_generator", sequenceName = "review_sequence", initialValue = 64)
	@GeneratedValue(generator = "review_generator")
	@SuppressWarnings("NullAway.Init")
	private Long id;

	@ManyToOne(optional = false)
	@SuppressWarnings("NullAway.Init")
	private Hotel hotel;

	@Column(nullable = false, name = "idx")
	@SuppressWarnings("NullAway.Init")
	private int index;

	@Column(nullable = false)
	@Enumerated(EnumType.ORDINAL)
	@SuppressWarnings("NullAway.Init")
	private Rating rating;

	@Column(nullable = false)
	@jakarta.persistence.Temporal(jakarta.persistence.TemporalType.DATE)
	@SuppressWarnings({ "deprecation", "NullAway.Init" })
	private Date checkInDate;

	@Column(nullable = false)
	@Enumerated(EnumType.ORDINAL)
	@SuppressWarnings("NullAway.Init")
	private TripType tripType;

	@Column(nullable = false)
	@SuppressWarnings("NullAway.Init")
	private String title;

	@Column(nullable = false, length = 5000)
	@SuppressWarnings("NullAway.Init")
	private String details;

	protected Review() {
	}

	public Review(Hotel hotel, int index, ReviewDetails details) {
		Assert.notNull(hotel, "'hotel' must not be null");
		Assert.notNull(details, "'details' must not be null");
		this.hotel = hotel;
		this.index = index;
		this.rating = details.getRating();
		this.checkInDate = details.getCheckInDate();
		this.tripType = details.getTripType();
		this.title = details.getTitle();
		this.details = details.getDetails();
	}

	public Hotel getHotel() {
		return this.hotel;
	}

	public int getIndex() {
		return this.index;
	}

	public Rating getRating() {
		return this.rating;
	}

	public void setRating(Rating rating) {
		this.rating = rating;
	}

	public Date getCheckInDate() {
		return this.checkInDate;
	}

	public void setCheckInDate(Date checkInDate) {
		this.checkInDate = checkInDate;
	}

	public TripType getTripType() {
		return this.tripType;
	}

	public void setTripType(TripType tripType) {
		this.tripType = tripType;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDetails() {
		return this.details;
	}

	public void setDetails(String details) {
		this.details = details;
	}

}
