/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package smoketest.bootstrapregistry.external.svn;

import org.jspecify.annotations.Nullable;

import org.springframework.boot.context.config.ConfigDataResource;

/**
 * A subversion {@link ConfigDataResource}.
 *
 * <AUTHOR> Webb
 */
class SubversionConfigDataResource extends ConfigDataResource {

	private final String location;

	private final @Nullable SubversionServerCertificate serverCertificate;

	SubversionConfigDataResource(String location, @Nullable String serverCertificate) {
		this.location = location;
		this.serverCertificate = SubversionServerCertificate.of(serverCertificate);
	}

	String getLocation() {
		return this.location;
	}

	@Nullable SubversionServerCertificate getServerCertificate() {
		return this.serverCertificate;
	}

	@Override
	public boolean equals(@Nullable Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null || getClass() != obj.getClass()) {
			return false;
		}
		SubversionConfigDataResource other = (SubversionConfigDataResource) obj;
		return this.location.equals(other.location);
	}

	@Override
	public int hashCode() {
		return this.location.hashCode();
	}

	@Override
	public String toString() {
		return this.location;
	}

}
