/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package smoketest.bootstrapregistry.external.svn;

import org.jspecify.annotations.Nullable;

/**
 * A client that can connect to a subversion server.
 *
 * <AUTHOR>
 */
public class SubversionClient {

	private final @Nullable SubversionServerCertificate serverCertificate;

	public SubversionClient(@Nullable SubversionServerCertificate serverCertificate) {
		this.serverCertificate = serverCertificate;
	}

	public String load(String location) {
		return "data from svn / " + location + "[" + this.serverCertificate + "]";
	}

}
