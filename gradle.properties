version=4.0.0-SNAPSHOT
latestVersion=true
spring.build-type=oss

org.gradle.caching=true
org.gradle.parallel=true
org.gradle.jvmargs=-Xmx2g -Dfile.encoding=UTF-8

assertjVersion=3.27.4
checkstyleToolVersion=10.12.4
commonsCodecVersion=1.19.0
graalVersion=22.3
hamcrestVersion=3.0
jacksonVersion=2.20.0-rc1
javaFormatVersion=0.0.47
junitJupiterVersion=5.13.4
kotlinVersion=2.2.0
mavenVersion=3.9.10
mockitoVersion=5.18.0
nativeBuildToolsVersion=0.11.0
snakeYamlVersion=2.4
springFrameworkVersion=7.0.0-SNAPSHOT
springFramework60xVersion=6.0.23
tomcatVersion=11.0.10
nullabilityPluginVersion=0.0.2
nullAwayVersion=0.12.8
errorProneVersion=2.41.0

kotlin.stdlib.default.dependency=false
