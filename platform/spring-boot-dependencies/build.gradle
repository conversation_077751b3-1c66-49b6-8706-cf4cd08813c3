/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "org.springframework.boot.bom"
	id "org.springframework.boot.deployed"
}

description = "Spring Boot Dependencies"

bom {
	upgrade {
		policy = "any"
		gitHub {
			issueLabels = ["type: dependency-upgrade"]
		}
	}
	library("ActiveMQ", "6.1.7") {
		group("org.apache.activemq") {
			modules = [
				"activemq-console",
				"activemq-spring"
			]
			bom("activemq-bom")
		}
		links {
			site("https://activemq.apache.org")
			docs("https://activemq.apache.org/components/classic/documentation")
			releaseNotes(version -> "https://activemq.apache.org/components/classic/download/classic-%02d-%02d-%02d"
				.formatted(version.componentInts()))
		}
	}
	library("Angus Mail", "2.0.4") {
		group("org.eclipse.angus") {
			modules = [
				"angus-core",
				"angus-mail",
				"dsn",
				"gimap",
				"imap",
				"jakarta.mail",
				"logging-mailhandler",
				"pop3",
				"smtp"
			]
		}
		links {
			site("https://github.com/eclipse-ee4j/angus-mail")
			releaseNotes("https://github.com/eclipse-ee4j/angus-mail/releases/tag/{version}")
		}
	}
	library("Artemis", "2.42.0") {
		group("org.apache.activemq") {
			bom("artemis-bom")
		}
		links {
			site("https://activemq.apache.org/components/artemis")
			javadoc("https://javadoc.io/doc/org.apache.activemq/artemis-jms-server/{version}", "org.apache.activemq.artemis.jms.server")
			releaseNotes("https://activemq.apache.org/components/artemis/download/release-notes-{version}")
		}
	}
	library("AspectJ", "1.9.24") {
		group("org.aspectj") {
			modules = [
				"aspectjrt",
				"aspectjtools",
				"aspectjweaver"
			]
		}
		links {
			site("https://eclipse.dev/aspectj")
			releaseNotes(version -> "https://github.com/eclipse-aspectj/aspectj/blob/master/docs/release/README-%s.%s.%s.adoc"
				.formatted(version.major(), version.minor(), version.patch()))
		}
	}
	library("AssertJ", "${assertjVersion}") {
		prohibit {
			contains "-M"
			contains "-RC"
			because "we don't want milestones or release candidates"
		}
		group("org.assertj") {
			bom("assertj-bom")
		}
		links {
			site("https://assertj.github.io/doc")
			releaseNotes("https://github.com/assertj/assertj/releases/tag/assertj-build-{version}")
		}
	}
	library("Awaitility", "4.3.0") {
		group("org.awaitility") {
			modules = [
				"awaitility",
				"awaitility-groovy",
				"awaitility-kotlin",
				"awaitility-scala"
			]
		}
		links {
			releaseNotes(version -> "https://github.com/awaitility/awaitility/wiki/ReleaseNotes%s.%s"
				.formatted(version.major(), version.minor()))
		}
	}
	library("Zipkin Reporter", "3.5.1") {
		group("io.zipkin.reporter2") {
			bom("zipkin-reporter-bom")
		}
		links {
			site("https://github.com/openzipkin/zipkin-reporter-java")
			releaseNotes("https://github.com/openzipkin/zipkin-reporter-java/releases/tag/{version}")
		}
	}
	library("Brave", "6.3.0") {
		group("io.zipkin.brave") {
			bom("brave-bom")
		}
		links {
			site("https://github.com/openzipkin/brave")
			releaseNotes("https://github.com/openzipkin/brave/releases/tag/{version}")
		}
	}
	library("Build Helper Maven Plugin", "3.6.1") {
		group("org.codehaus.mojo") {
			plugins = [
				"build-helper-maven-plugin"
			]
		}
		links {
			site("https://www.mojohaus.org/build-helper-maven-plugin")
			releaseNotes("https://github.com/mojohaus/build-helper-maven-plugin/releases/tag/{version}")
		}
	}
	library("Byte Buddy", "1.17.6") {
		group("net.bytebuddy") {
			modules = [
				"byte-buddy",
				"byte-buddy-agent"
			]
		}
		links {
			site("https://bytebuddy.net")
			docs("https://bytebuddy.net/#/tutorial")
			releaseNotes("https://github.com/raphw/byte-buddy/releases/tag/byte-buddy-{version}")
		}
	}
	library("cache2k", "2.6.1.Final") {
		group("org.cache2k") {
			modules = [
					"cache2k-api",
					"cache2k-config",
					"cache2k-core",
					"cache2k-jcache",
					"cache2k-micrometer",
					"cache2k-spring"
			]
		}
		links {
			site("https://cache2k.org")
			releaseNotes("https://github.com/cache2k/cache2k/releases/tag/v{version}")
		}
	}
	library("Caffeine", "3.2.2") {
		group("com.github.ben-manes.caffeine") {
			modules = [
				"caffeine",
				"guava",
				"jcache",
				"simulator"
			]
		}
		links {
			site("https://github.com/ben-manes/caffeine")
			javadoc("https://javadoc.io/doc/com.github.ben-manes.caffeine/caffeine/{version}", "com.github.benmanes.caffeine")
			docs("https://github.com/ben-manes/caffeine/wiki")
			releaseNotes("https://github.com/ben-manes/caffeine/releases/tag/v{version}")
		}
	}
	library("Cassandra Driver", "4.19.0") {
		group("org.apache.cassandra") {
			bom("java-driver-bom") {
				permit("com.datastax.oss:native-protocol")
			}
			modules = [
				"java-driver-core"
			]
		}
	}
	library("Classmate", "1.7.0") {
		group("com.fasterxml") {
			modules = [
				"classmate"
			]
		}
		links {
			site("https://github.com/FasterXML/java-classmate")
		}
	}
	library("Commons Codec", "${commonsCodecVersion}") {
		group("commons-codec") {
			modules = [
				"commons-codec"
			]
		}
		links {
			site("https://commons.apache.org/proper/commons-codec")
			releaseNotes("https://commons.apache.org/proper/commons-codec/changes.html#a{version}")
		}
	}
	library("Commons DBCP2", "2.13.0") {
		group("org.apache.commons") {
			modules = [
				"commons-dbcp2"
			]
		}
		links {
			site("https://commons.apache.org/proper/commons-dbcp")
			releaseNotes("https://commons.apache.org/proper/commons-dbcp/changes-report.html#a{version}")
		}
	}
	library("Commons Lang3", "3.18.0") {
		group("org.apache.commons") {
			modules = [
				"commons-lang3"
			]
		}
		links {
			site("https://commons.apache.org/proper/commons-lang")
			releaseNotes("https://commons.apache.org/proper/commons-lang/changes.html#a{version}")
		}
	}
	library("Commons Logging", "1.3.5") {
		group("commons-logging") {
			modules = [
					"commons-logging"
			]
		}
		links {
			site("https://commons.apache.org/proper/commons-logging")
			releaseNotes("https://commons.apache.org/proper/commons-logging/changes-report.html#a{version}")
		}
	}
	library("Commons Pool", "1.6") {
		group("commons-pool") {
			modules = [
				"commons-pool"
			]
		}
	}
	library("Commons Pool2", "2.12.1") {
		group("org.apache.commons") {
			modules = [
				"commons-pool2"
			]
		}
		links {
			site("https://commons.apache.org/proper/commons-pool")
		}
	}
	library("Couchbase Client", "3.9.0") {
		group("com.couchbase.client") {
			modules = [
				"java-client"
			]
		}
		links {
			site("https://docs.couchbase.com/java-sdk/current/hello-world/overview.html")
			javadoc("https://javadoc.io/doc/com.couchbase.client/java-client/{version}", "com.couchbase.client")
			releaseNotes("https://docs.couchbase.com/java-sdk/current/project-docs/sdk-release-notes.html")
		}
	}
	library("Crac", "1.5.0") {
		group("org.crac") {
			modules = [
				"crac"
			]
		}
	}
	library("CycloneDX Maven Plugin", "2.9.1") {
		group("org.cyclonedx") {
			plugins = [
				"cyclonedx-maven-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/CycloneDX/cyclonedx-maven-plugin/releases/tag/cyclonedx-maven-plugin-{version}")
		}
	}
	library("DB2 JDBC", "12.1.2.0") {
		group("com.ibm.db2") {
			modules = [
				"jcc"
			]
		}
	}
	library("Dependency Management Plugin", "1.1.7") {
		group("io.spring.gradle") {
			modules = [
				"dependency-management-plugin"
			]
		}
		links {
			site("https://github.com/spring-gradle-plugins/dependency-management-plugin")
			docs("https://docs.spring.io/dependency-management-plugin/docs/{version}/reference/html")
			releaseNotes("https://github.com/spring-gradle-plugins/dependency-management-plugin/releases/tag/v{version}")
		}
	}
	library("Derby", "10.16.1.1") {
		prohibit {
			versionRange "[10.17.1.0,)"
			because "it requires Java 21"
		}
		group("org.apache.derby") {
			modules = [
				"derby",
				"derbyclient",
				"derbynet",
				"derbyoptionaltools",
				"derbyshared",
				"derbytools"
			]
		}
	}
	library("Ehcache3", "3.10.8") {
		group("org.ehcache") {
			modules = [
					"ehcache",
					"ehcache" {
						classifier = 'jakarta'
					},
					"ehcache-clustered",
					"ehcache-transactions",
					"ehcache-transactions" {
						classifier = 'jakarta'
					}
			]
		}
		links {
			site("https://www.ehcache.org")
			releaseNotes("https://github.com/ehcache/ehcache3/releases/tag/v{version}")
		}
	}
	library("Elasticsearch Client", "9.1.1") {
		prohibit {
			contains "-alpha"
			contains "-beta"
			contains "-rc"
			because "we don't want preview releases"
		}
		alignWith {
			version {
				from "org.springframework.data:spring-data-elasticsearch"
				managedBy "Spring Data Bom"
			}
		}
		group("co.elastic.clients") {
			modules = [
				"elasticsearch-java"
			]
		}
		links {
			releaseNotes(version -> "https://www.elastic.co/docs/release-notes/elasticsearch/clients/java/%s".formatted(version.toString("-")))
			javadoc("elasticsearch-java", version -> "https://javadoc.io/doc/co.elastic.clients/elasticsearch-java/%s/index.html".formatted(version), "co.elastic.clients.elasticsearch", "co.elastic.clients.transport")
		}
	}
	library("Flyway", "11.11.0") {
		group("org.flywaydb") {
			modules = [
				"flyway-commandline",
				"flyway-core",
				"flyway-database-cassandra",
				"flyway-database-db2",
				"flyway-database-derby",
				"flyway-database-hsqldb",
				"flyway-database-informix",
				"flyway-database-mongodb",
				"flyway-database-oracle",
				"flyway-database-postgresql",
				"flyway-database-redshift",
				"flyway-database-saphana",
				"flyway-database-snowflake",
				"flyway-database-sybasease",
				"flyway-firebird",
				"flyway-gcp-bigquery",
				"flyway-gcp-spanner",
				"flyway-mysql",
				"flyway-singlestore",
				"flyway-sqlserver"
			]
			plugins = [
				"flyway-maven-plugin"
			]
		}
		links {
			site("https://documentation.red-gate.com/flyway")
			javadoc("https://javadoc.io/doc/org.flywaydb/flyway-core/{version}", "org.flywaydb")
			releaseNotes("https://documentation.red-gate.com/flyway/release-notes-and-older-versions/release-notes-for-flyway-engine")
		}
	}
	library("FreeMarker", "2.3.34") {
		group("org.freemarker") {
			modules = [
				"freemarker"
			]
		}
		links {
			site("https://freemarker.apache.org")
			releaseNotes(version -> "https://freemarker.apache.org/docs/versions_%s.html"
				.formatted(version.toString("_")))
		}
	}
	library("Git Commit ID Maven Plugin", "9.0.2") {
		group("io.github.git-commit-id") {
			plugins = [
				"git-commit-id-maven-plugin"
			]
		}
		links {
			site("https://github.com/git-commit-id/git-commit-id-maven-plugin")
			releaseNotes("https://github.com/git-commit-id/git-commit-id-maven-plugin/releases/tag/v{version}")
		}
	}
	library("Glassfish JAXB", "4.0.5") {
		group("org.glassfish.jaxb") {
			bom("jaxb-bom") {
				permit("com.sun.istack:istack-commons-runtime")
				permit("com.sun.xml.bind:jaxb-core")
				permit("com.sun.xml.bind:jaxb-impl")
				permit("com.sun.xml.bind:jaxb-jxc")
				permit("com.sun.xml.bind:jaxb-osgi")
				permit("com.sun.xml.bind:jaxb-xjc")
				permit("com.sun.xml.fastinfoset:FastInfoset")
				permit("jakarta.activation:jakarta.activation-api")
				permit("jakarta.xml.bind:jakarta.xml.bind-api")
				permit("org.eclipse.angus:angus-activation")
				permit("org.jvnet.staxex:stax-ex")
			}
		}
		links {
			releaseNotes("https://github.com/eclipse-ee4j/jaxb-ri/releases/tag/{version}-RI")
		}
	}
	library("Glassfish JSTL", "3.0.1") {
		group("org.glassfish.web") {
			modules = [
				"jakarta.servlet.jsp.jstl"
			]
		}
	}
	library("GraphQL Java", "24.1") {
		prohibit {
			startsWith(["2018-", "2019-", "2020-", "2021-", "230521-"])
			because "we don't want thses snapshots"
		}
		alignWith {
			version {
				from "org.springframework.graphql:spring-graphql"
			}
		}
		group("com.graphql-java") {
			modules = [
					"graphql-java"
			]
		}
		links {
			site("https://www.graphql-java.com")
			javadoc("https://javadoc.io/doc/com.graphql-java/graphql-java/{version}", "graphql")
			releaseNotes("https://github.com/graphql-java/graphql-java/releases/tag/v{version}")
		}
	}
	library("Groovy", "4.0.28") {
		prohibit {
			contains "-alpha-"
			because "we don't want alpha dependencies"
		}
		group("org.apache.groovy") {
			bom("groovy-bom")
		}
		links {
			site("https://groovy-lang.org")
		}
	}
	library("Gson", "2.13.1") {
		group("com.google.code.gson") {
			modules = [
				"gson"
			]
		}
		links {
			site("https://github.com/google/gson")
			javadoc("https://javadoc.io/doc/com.google.code.gson/gson/{version}", "com.google.gson")
			releaseNotes("https://github.com/google/gson/releases/tag/gson-parent-{version}")
		}
	}
	library("H2", "2.3.232") {
		group("com.h2database") {
			modules = [
				"h2"
			]
		}
		links {
			site("https://www.h2database.com")
			javadoc("https://www.h2database.com/javadoc", "org.h2")
			releaseNotes("https://github.com/h2database/h2database/releases/tag/version-{version}")
		}
	}
	library("Hamcrest", "${hamcrestVersion}") {
		group("org.hamcrest") {
			modules = [
				"hamcrest",
				"hamcrest-core",
				"hamcrest-library"
			]
		}
		links {
			releaseNotes("https://github.com/hamcrest/JavaHamcrest/releases/tag/v{version}")
		}
	}
	library("Hazelcast", "5.5.0") {
		group("com.hazelcast") {
			modules = [
				"hazelcast",
				"hazelcast-spring"
			]
		}
		links {
			site("https://hazelcast.com")
			javadoc("https://docs.hazelcast.org/docs/{version}/javadoc", "com.hazelcast")
			releaseNotes("https://github.com/hazelcast/hazelcast/releases/tag/v{version}")
		}
	}
	library("Hibernate", "7.1.0.Final") {
		group("org.hibernate.orm") {
			modules = [
				"hibernate-agroal",
				"hibernate-ant",
				"hibernate-c3p0",
				"hibernate-community-dialects",
				"hibernate-core",
				"hibernate-envers",
				"hibernate-graalvm",
				"hibernate-hikaricp",
				"hibernate-jcache",
				"hibernate-jpamodelgen",
				"hibernate-micrometer",
				"hibernate-proxool",
				"hibernate-spatial",
				"hibernate-testing",
				"hibernate-vibur"
			]
		}
		links {
			site("https://hibernate.org/orm")
			javadoc(version -> "https://docs.jboss.org/hibernate/orm/%s.%s/javadocs"
				.formatted(version.major(), version.minor()), "org.hibernate.boot", "org.hibernate.resource")
			docs(version -> "https://hibernate.org/orm/documentation/%s.%s"
				.formatted(version.major(), version.minor()))
			releaseNotes(version -> "https://github.com/hibernate/hibernate-orm/releases/tag/%s"
				.formatted(version.toString().replace(".Final", "")))
			add("userguide", version -> "https://docs.jboss.org/hibernate/orm/%s.%s/userguide/html_single/Hibernate_User_Guide.html"
				.formatted(version.major(), version.minor()))
		}
	}
	library("Hibernate Validator", "9.0.1.Final") {
		group("org.hibernate.validator") {
			modules = [
				"hibernate-validator",
				"hibernate-validator-annotation-processor"
			]
		}
	}
	library("HikariCP", "7.0.1") {
		group("com.zaxxer") {
			modules = [
				"HikariCP"
			]
		}
		links {
			site("https://github.com/brettwooldridge/HikariCP")
			javadoc("https://javadoc.io/doc/com.zaxxer/HikariCP/{version}/com.zaxxer.hikari", "com.zaxxer.hikari")
		}
	}
	library("HSQLDB", "2.7.3") {
		prohibit {
			versionRange "[2.7.4]"
			because "it contains a bug that breaks Spring Data (https://sourceforge.net/p/hsqldb/bugs/1725/)"
		}
		group("org.hsqldb") {
			modules = [
				"hsqldb"
			]
		}
	}
	library("HtmlUnit", "4.14.0") {
		group("org.htmlunit") {
			modules = [
				"htmlunit"
			]
		}
		links {
			site("https://www.htmlunit.org")
			releaseNotes("https://github.com/HtmlUnit/htmlunit/releases/tag/{version}")
		}
	}
	library("HttpAsyncClient", "4.1.5") {
		prohibit {
			contains "-alpha"
			contains "-beta"
			contains "-rc"
			because "we don't want preview releases"
		}
		group("org.apache.httpcomponents") {
			modules = [
				"httpasyncclient"
			]
		}
	}
	library("HttpClient5", "5.5") {
		prohibit {
			contains "-alpha"
			contains "-beta"
			contains "-rc"
			because "we don't want preview releases"
		}
		group("org.apache.httpcomponents.client5") {
			modules = [
				"httpclient5",
				"httpclient5-cache",
				"httpclient5-fluent"
			]
		}
	}
	library("HttpCore", "4.4.16") {
		group("org.apache.httpcomponents") {
			modules = [
				"httpcore",
				"httpcore-nio"
			]
		}
	}
	library("HttpCore5", "5.3.4") {
		group("org.apache.httpcomponents.core5") {
			modules = [
				"httpcore5",
				"httpcore5-h2",
				"httpcore5-reactive"
			]
		}
	}
	library("Infinispan", "15.2.5.Final") {
		group("org.infinispan") {
			bom("infinispan-bom")
		}
		links {
			site("https://infinispan.org")
			javadoc(version -> "https://docs.jboss.org/infinispan/%s.%s/apidocs".formatted(version.major(), version.minor()), "org.infinispan")
			releaseNotes("https://github.com/infinispan/infinispan/releases/tag/{version}")
		}
	}
	library("InfluxDB Java", "2.25") {
		group("org.influxdb") {
			modules = [
				"influxdb-java"
			]
		}
		links {
			site("https://github.com/influxdata/influxdb-java")
			javadoc("https://javadoc.io/doc/org.influxdb/influxdb-java/{version}", "org.influxdb")
			releaseNotes("https://github.com/influxdata/influxdb-java/releases/tag/influxdb-java-{version}")
		}
	}
	library("Jackson Bom", "${jacksonVersion}") {
		group("com.fasterxml.jackson") {
			bom("jackson-bom")
		}
		links {
			releaseNotes("https://github.com/FasterXML/jackson/wiki/Jackson-Release-{version}")
		}
	}
	library("Jakarta Activation", "2.1.3") {
		group("jakarta.activation") {
			modules = [
				"jakarta.activation-api"
			]
		}
		links {
			site("https://github.com/jakartaee/jaf-api")
			javadoc(version -> "https://jakarta.ee/specifications/activation/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.activation")
			releaseNotes("https://github.com/jakartaee/jaf-api/releases/tag/{version}")
		}
	}
	library("Jakarta Annotation", "3.0.0") {
		group("jakarta.annotation") {
			modules = [
				"jakarta.annotation-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/annotations/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.annotation")
		}
	}
	library("Jakarta Inject", "2.0.1") {
		group("jakarta.inject") {
			modules = [
				"jakarta.inject-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/dependency-injection/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.inject")
		}
	}
	library("Jakarta JMS", "3.1.0") {
		group("jakarta.jms") {
			modules = [
				"jakarta.jms-api"
			]
		}
		links {
			site(version -> "https://jakarta.ee/specifications/messaging/%s.%s"
				.formatted(version.major(), version.minor()))
			javadoc(version -> "https://jakarta.ee/specifications/messaging/%s.%s/apidocs/jakarta.messaging"
				.formatted(version.major(), version.minor()), "jakarta.jms")
		}
	}
	library("Jakarta Json", "2.1.3") {
		group("jakarta.json") {
			modules = [
				"jakarta.json-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/jsonp/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.json")
			releaseNotes("https://github.com/jakartaee/jsonp-api/releases/tag/{version}-RELEASE")
		}
	}
	library("Jakarta Json Bind", "3.0.1") {
		group("jakarta.json.bind") {
			modules = [
				"jakarta.json.bind-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/jsonb/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.json.bind")
		}
	}
	library("Jakarta Mail", "2.1.3") {
		group("jakarta.mail") {
			modules = [
				"jakarta.mail-api"
			]
		}
		links {
			site(version -> "https://jakarta.ee/specifications/mail/%s.%s"
				.formatted(version.major(), version.minor()))
			javadoc(version -> "https://jakarta.ee/specifications/mail/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.mail")
			releaseNotes("https://github.com/jakartaee/mail-api/releases/tag/{version}")
		}
	}
	library("Jakarta Management", "1.1.4") {
		group("jakarta.management.j2ee") {
			modules = [
				"jakarta.management.j2ee-api"
			]
		}
	}
	library("Jakarta Persistence", "3.2.0") {
		group("jakarta.persistence") {
			modules = [
				"jakarta.persistence-api"
			]
		}
		links {
			site(version -> "https://jakarta.ee/specifications/persistence/%s.%s"
				.formatted(version.major(), version.minor()))
			javadoc(version -> "https://jakarta.ee/specifications/persistence/%s.%s/apidocs/jakarta.persistence"
				.formatted(version.major(), version.minor()), "jakarta.persistence")
			releaseNotes(version -> "https://github.com/jakartaee/persistence/releases/tag/%s.%s-%s-RELEASE"
				.formatted(version.major(), version.minor(), version))
		}
	}
	library("Jakarta Servlet", "6.1.0") {
		group("jakarta.servlet") {
			modules = [
				"jakarta.servlet-api"
			]
		}
		links {
			site(version -> "https://jakarta.ee/specifications/servlet/%s.%s"
				.formatted(version.major(), version.minor()))
			javadoc(version -> "https://jakarta.ee/specifications/servlet/%s.%s/apidocs/jakarta.servlet"
				.formatted(version.major(), version.minor()), "jakarta.servlet")
		}
	}
	library("Jakarta Servlet JSP JSTL", "3.0.2") {
		group("jakarta.servlet.jsp.jstl") {
			modules = [
				"jakarta.servlet.jsp.jstl-api"
			]
		}
		links {
			releaseNotes("https://github.com/jakartaee/tags/releases/tag/{version}-RELEASE")
		}
	}
	library("Jakarta Transaction", "2.0.1") {
		group("jakarta.transaction") {
			modules = [
				"jakarta.transaction-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/transactions/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.transaction")
		}
	}
	library("Jakarta Validation", "3.1.1") {
		group("jakarta.validation") {
			modules = [
				"jakarta.validation-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/bean-validation/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.validation")
			releaseNotes("https://github.com/jakartaee/validation/releases/tag/{version}")
		}
	}
	library("Jakarta WebSocket", "2.2.0") {
		group("jakarta.websocket") {
			modules = [
				"jakarta.websocket-api",
				"jakarta.websocket-client-api"
			]
		}
		links {
			javadoc("jakarta-websocket-server", version -> "https://jakarta.ee/specifications/websocket/%s.%s/apidocs/server"
				.formatted(version.major(), version.minor()), "jakarta.websocket.server")
			javadoc("jakarta-websocket-client", version -> "https://jakarta.ee/specifications/websocket/%s.%s/apidocs/client"
				.formatted(version.major(), version.minor()), "jakarta.websocket")
		}
	}
	library("Jakarta WS RS", "4.0.0") {
		group("jakarta.ws.rs") {
			modules = [
				"jakarta.ws.rs-api"
			]
		}
		links {
			javadoc(version -> "https://jakarta.ee/specifications/restful-ws/%s.%s/apidocs"
				.formatted(version.major(), version.minor()), "jakarta.ws.rs")
		}
	}
	library("Jakarta XML Bind", "4.0.2") {
		group("jakarta.xml.bind") {
			modules = [
				"jakarta.xml.bind-api"
			]
		}
		links {
			releaseNotes("https://github.com/jakartaee/jaxb-api/releases/tag/{version}")
			javadoc(version -> "https://jakarta.ee/specifications/xml-binding/%s.%s/apidocs/jakarta.xml.bind"
				.formatted(version.major(), version.minor()), "jakarta.xml.bind")
		}
	}
	library("Jakarta XML SOAP", "3.0.2") {
		group("jakarta.xml.soap") {
			modules = [
					"jakarta.xml.soap-api"
			]
		}
		links {
			releaseNotes("https://github.com/jakartaee/saaj-api/releases/tag/{version}")
		}
	}
	library("Jakarta XML WS", "4.0.2") {
		group("jakarta.xml.ws") {
			modules = [
				"jakarta.xml.ws-api"
			]
		}
		links {
			releaseNotes("https://github.com/jakartaee/jax-ws-api/releases/tag/{version}")
		}
	}
	library("Janino", "3.1.12") {
		group("org.codehaus.janino") {
			modules = [
				"commons-compiler",
				"commons-compiler-jdk",
				"janino"
			]
		}
	}
	library("Javax Cache", "1.1.1") {
		group("javax.cache") {
			modules = [
				"cache-api"
			]
		}
		links {
			javadoc("https://javadoc.io/doc/javax.cache/cache-api/{version}", "javax.cache")
		}
	}
	library("Javax Money", "1.1") {
		group("javax.money") {
			modules = [
				"money-api"
			]
		}
	}
	library("Jaxen", "2.0.0") {
		group("jaxen") {
			modules = [
				"jaxen"
			]
		}
		links {
			releaseNotes("https://github.com/jaxen-xpath/jaxen/releases/tag/v{version}")
		}
	}
	library("Jaybird", "6.0.2") {
		prohibit {
			endsWith ".java8"
			because "we use the .java11 version"
		}
		group("org.firebirdsql.jdbc") {
			modules = [
				"jaybird"
			]
		}
		links {
			releaseNotes(version -> "https://github.com/FirebirdSQL/jaybird/releases/tag/v%s"
				.formatted(version.toString().replace(".java11", "")))
		}
	}
	library("JBoss Logging", "3.6.1.Final") {
		group("org.jboss.logging") {
			modules = [
				"jboss-logging"
			]
		}
		links {
			releaseNotes("https://github.com/jboss-logging/jboss-logging/releases/tag/{version}")
		}
	}
	library("JDOM2", "2.0.6.1") {
		group("org.jdom") {
			modules = [
				"jdom2"
			]
		}
		links {
			releaseNotes("https://github.com/hunterhacker/jdom/releases/tag/JDOM-{version}")
		}
	}
	library("Jedis", "6.1.0") {
		alignWith {
			property {
				name "jedis"
				of "org.springframework.data:spring-data-redis"
				managedBy "Spring Data Bom"
			}
		}
		group("redis.clients") {
			modules = [
				"jedis"
			]
		}
		links {
			site("https://github.com/redis/jedis")
			releaseNotes("https://github.com/redis/jedis/releases/tag/v{version}")
		}
	}
	library("Jersey", "4.0.0-M2") {
		group("org.glassfish.jersey") {
			bom("jersey-bom")
		}
		links {
			site("https://github.com/eclipse-ee4j/jersey")
			javadoc("https://javadoc.io/doc/org.glassfish.jersey.core/jersey-server/{version}", "org.glassfish.jersey.server")
			releaseNotes("https://github.com/eclipse-ee4j/jersey/releases/tag/{version}")
		}
	}
	library("Jetty Reactive HTTPClient", "4.0.10") {
		group("org.eclipse.jetty") {
			modules = [
				"jetty-reactive-httpclient"
			]
		}
	}
	library("Jetty", "12.0.25") {
		prohibit {
			contains ".alpha"
			because "we don't want alpha dependencies"
		}
		group("org.eclipse.jetty.ee10") {
			bom("jetty-ee10-bom")
		}
		group("org.eclipse.jetty") {
			bom("jetty-bom")
		}
		links {
			site("https://eclipse.dev/jetty")
			javadoc(version -> "https://javadoc.jetty.org/jetty-%s".formatted(version.major()), "org.eclipse.jetty")
			releaseNotes("https://github.com/jetty/jetty.project/releases/tag/jetty-{version}")
		}
	}
	library("JMustache", "1.16") {
		group("com.samskivert") {
			modules = [
				"jmustache"
			]
		}
	}
	library("jOOQ", "3.19.25") {
		prohibit {
			versionRange "[3.20.0,)"
			because "it requires Java 21"
		}
		group("org.jooq") {
			bom("jooq-bom")
			plugins = [
				"jooq-codegen-maven"
			]
		}
		links {
			site("https://www.jooq.org")
			javadoc("https://www.jooq.org/javadoc/{version}", "org.jooq")
			docs("https://www.jooq.org/doc/{version}/manual-single-page")
			releaseNotes("https://github.com/jOOQ/jOOQ/releases/tag/version-{version}")
		}
	}
	library("Json Path", "2.9.0") {
		group("com.jayway.jsonpath") {
			modules = [
				"json-path",
				"json-path-assert"
			]
		}
		links {
			site("https://github.com/json-path/JsonPath")
			releaseNotes("https://github.com/json-path/JsonPath/releases/tag/json-path-{version}")
		}
	}
	library("Json-smart", "2.6.0") {
		group("net.minidev") {
			modules = [
				"json-smart"
			]
		}
		links {
			site("https://github.com/netplex/json-smart-v2")
			releaseNotes("https://github.com/netplex/json-smart-v2/releases/tag/{version}")
		}
	}
	library("JsonAssert", "1.5.3") {
		prohibit {
			contains "-rc"
			because "we don't want release candidates"
		}
		group("org.skyscreamer") {
			modules = [
				"jsonassert"
			]
		}
		links {
			site("https://github.com/skyscreamer/JSONassert")
			releaseNotes("https://github.com/skyscreamer/JSONassert/releases/tag/jsonassert-{version}")
		}
	}
	library("JSpecify", "1.0.0") {
		group("org.jspecify") {
			modules = [
					"jspecify"
			]
		}
		links {
			site("https://jspecify.dev")
			docs("https://jspecify.dev/docs/user-guide")
			javadoc("https://jspecify.dev/docs/api", "org.jspecify")
			releaseNotes("https://github.com/jspecify/jspecify/releases/tag/v{version}")
		}
	}
	library("JTDS", "1.3.1") {
		group("net.sourceforge.jtds") {
			modules = [
				"jtds"
			]
		}
	}
	library("JUnit", "4.13.2") {
		group("junit") {
			modules = [
				"junit"
			]
		}
		links {
			releaseNotes("https://github.com/junit-team/junit4/blob/HEAD/doc/ReleaseNotes{version}.md")
		}
	}
	library("JUnit Jupiter", "${junitJupiterVersion}") {
		prohibit {
			contains "-M"
			because "we don't want milestones"
		}
		group("org.junit") {
			bom("junit-bom")
		}
		links {
			site("https://junit.org/junit5")
			javadoc("junit-platform-engine", version -> "https://junit.org/junit5/docs/%s/api/org.junit.platform.engine".formatted(version), "org.junit.platform")
			javadoc("junit-jupiter-api", version -> "https://junit.org/junit5/docs/%s/api/org.junit.jupiter.api".formatted(version), "org.junit.jupiter.api")
			docs("https://junit.org/junit5/docs/{version}/user-guide")
			releaseNotes("https://junit.org/junit5/docs/{version}/release-notes")
		}
	}
	library("Kafka", "4.0.0") {
		group("org.apache.kafka") {
			modules = [
				"connect",
				"connect-api",
				"connect-basic-auth-extension",
				"connect-file",
				"connect-json",
				"connect-mirror",
				"connect-mirror-client",
				"connect-runtime",
				"connect-transforms",
				"generator",
				"kafka-clients",
				"kafka-clients" {
					classifier = "test"
				},
				"kafka-log4j-appender",
				"kafka-metadata",
				"kafka-raft",
				"kafka-server",
				"kafka-server-common",
				"kafka-server-common" {
					classifier = "test"
				},
				"kafka-shell",
				"kafka-storage",
				"kafka-storage-api",
				"kafka-streams",
				"kafka-streams-scala_2.12",
				"kafka-streams-scala_2.13",
				"kafka-streams-test-utils",
				"kafka-tools",
				"kafka_2.12",
				"kafka_2.12" {
					classifier = "test"
				},
				"kafka_2.13",
				"kafka_2.13" {
					classifier = "test"
				},
				"trogdor"
			]
		}
		links {
			site("https://kafka.apache.org")
			javadoc(version -> "https://kafka.apache.org/%s%s/javadoc".formatted(version.major(), version.minor()), "org.apache.kafka")
			releaseNotes("https://downloads.apache.org/kafka/{version}/RELEASE_NOTES.html")
		}
	}
	library("Kotlin", "${kotlinVersion}") {
		group("org.jetbrains.kotlin") {
			bom("kotlin-bom")
			plugins = [
				"kotlin-maven-plugin"
			]
		}
		links {
			site("https://kotlinlang.org")
			docs("https://kotlinlang.org/docs/reference")
			releaseNotes("https://github.com/JetBrains/kotlin/releases/tag/v{version}")
		}
	}
	library("Kotlin Coroutines", "1.10.2") {
		group("org.jetbrains.kotlinx") {
			bom("kotlinx-coroutines-bom")
		}
		links {
			site("https://github.com/Kotlin/kotlinx.coroutines")
			releaseNotes("https://github.com/Kotlin/kotlinx.coroutines/releases/tag/{version}")
		}
	}
	library("Kotlin Serialization", "1.9.0") {
		group("org.jetbrains.kotlinx") {
			bom("kotlinx-serialization-bom")
		}
		links {
			site("https://github.com/Kotlin/kotlinx.serialization")
			releaseNotes("https://github.com/Kotlin/kotlinx.serialization/releases/tag/v{version}")
		}
	}
	library("Lettuce", "6.8.0.RELEASE") {
		prohibit {
			contains ".BETA"
			because "we don't want betas"
		}
		alignWith {
			property {
				name "lettuce"
				of "org.springframework.data:spring-data-redis"
				managedBy "Spring Data Bom"
			}
		}
		group("io.lettuce") {
			modules = [
				"lettuce-core"
			]
		}
		links {
			site("https://github.com/lettuce-io/lettuce-core")
			javadoc("https://javadoc.io/doc/io.lettuce/lettuce-core/{version}", "io.lettuce.core")
			docs("https://lettuce.io/core/{version}/reference/index.html")
			releaseNotes("https://github.com/lettuce-io/lettuce-core/releases/tag/{version}")
		}
	}
	library("Liquibase", "4.33.0") {
		group("org.liquibase") {
			modules = [
				"liquibase-cdi",
				"liquibase-core"
			]
			plugins = [
				"liquibase-maven-plugin"
			]
		}
		links {
			site("https://www.liquibase.com")
			javadoc("https://javadoc.io/doc/org.liquibase/liquibase-core/{version}", "liquibase")
			releaseNotes("https://github.com/liquibase/liquibase/releases/tag/v{version}")
		}
	}
	library("Log4j2", "2.24.3") {
		prohibit {
			contains "-alpha"
			contains "-beta"
			because "we don't want alphas or betas"
		}
		group("org.apache.logging.log4j") {
			bom("log4j-bom") {
				permit("biz.aQute.bnd:biz.aQute.bnd.annotation")
				permit("com.github.spotbugs:spotbugs-annotations")
				permit("org.apache.logging:logging-parent")
				permit("org.apache.maven.plugin-tools:maven-plugin-annotations")
				permit("org.jspecify:jspecify")
				permit("org.osgi:org.osgi.annotation.bundle")
				permit("org.osgi:org.osgi.annotation.versioning")
				permit("org.osgi:osgi.annotation")
			}
		}
		links {
			site("https://logging.apache.org/log4j")
			javadoc("log4j-api", version -> "https://logging.apache.org/log4j/%s.x/javadoc/log4j-api".formatted(version.major()))
			javadoc("log4j-core", version -> "https://logging.apache.org/log4j/%s.x/javadoc/log4j-core".formatted(version.major()), "org.apache.logging.log4j.core")
			docs(version -> "https://logging.apache.org/log4j/%s.x/manual".formatted(version.major()))
			releaseNotes("https://github.com/apache/logging-log4j2/releases/tag/rel%2F{version}")
		}
	}
	library("Logback", "1.5.18") {
		group("ch.qos.logback") {
			modules = [
				"logback-classic",
				"logback-core"
			]
		}
		links {
			site("https://logback.qos.ch")
			javadoc("https://logback.qos.ch/apidocs/ch.qos.logback.core", "ch.qos.logback")
		}
	}
	library("Lombok", "1.18.38") {
		group("org.projectlombok") {
			modules = [
				"lombok"
			]
		}
		links {
			site("https://projectlombok.org")
			javadoc("https://projectlombok.org/api")
		}
	}
	library("MariaDB", "3.5.5") {
		group("org.mariadb.jdbc") {
			modules = [
				"mariadb-java-client"
			]
		}
		links {
			site("https://mariadb.com/kb/en/mariadb-connector-j")
			releaseNotes(version -> "https://mariadb.com/kb/en/mariadb-connector-j-%s-release-notes"
				.formatted(version.toString("-")))
		}
	}
	library("Maven AntRun Plugin", "3.1.0") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-antrun-plugin"
			]
		}
	}
	library("Maven Assembly Plugin", "3.7.1") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-assembly-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-assembly-plugin/releases/tag/maven-assembly-plugin-{version}")
		}
	}
	library("Maven Clean Plugin", "3.5.0") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-clean-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-clean-plugin/releases/tag/maven-clean-plugin-{version}")
		}
	}
	library("Maven Compiler Plugin", "3.14.0") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-compiler-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-compiler-plugin/releases/tag/maven-compiler-plugin-{version}")
		}
	}
	library("Maven Dependency Plugin", "3.8.1") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-dependency-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-dependency-plugin/releases/tag/maven-dependency-plugin-{version}")
		}
	}
	library("Maven Deploy Plugin", "3.1.4") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-deploy-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-deploy-plugin/releases/tag/maven-deploy-plugin-{version}")
		}
	}
	library("Maven Enforcer Plugin", "3.6.1") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-enforcer-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-enforcer/releases/tag/enforcer-{version}")
		}
	}
	library("Maven Failsafe Plugin", "3.5.3") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-failsafe-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-surefire/releases/tag/surefire-{version}")
		}
	}
	library("Maven Help Plugin", "3.5.1") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-help-plugin"
			]
		}
	}
	library("Maven Install Plugin", "3.1.4") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-install-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-install-plugin/releases/tag/maven-install-plugin-{version}")
		}
	}
	library("Maven Invoker Plugin", "3.9.1") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-invoker-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-invoker-plugin/releases/tag/maven-invoker-plugin-{version}")
		}
	}
	library("Maven Jar Plugin", "3.4.2") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-jar-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-jar-plugin/releases/tag/maven-jar-plugin-{version}")
		}
	}
	library("Maven Javadoc Plugin", "3.11.2") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-javadoc-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-javadoc-plugin/releases/tag/maven-javadoc-plugin-{version}")
		}
	}
	library("Maven Resources Plugin", "3.3.1") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-resources-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-resources-plugin/releases/tag/maven-resources-plugin-{version}")
		}
	}
	library("Maven Shade Plugin", "3.6.0") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-shade-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-shade-plugin/releases/tag/maven-shade-plugin-{version}")
		}
	}
	library("Maven Source Plugin", "3.3.1") {
		prohibit {
			contains "-beta-"
			because "we don't want betas"
		}
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-source-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-source-plugin/releases/tag/maven-source-plugin-{version}")
		}
	}
	library("Maven Surefire Plugin", "3.5.3") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-surefire-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-surefire/releases/tag/surefire-{version}")
		}
	}
	library("Maven War Plugin", "3.4.0") {
		group("org.apache.maven.plugins") {
			plugins = [
				"maven-war-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/apache/maven-war-plugin/releases/tag/maven-war-plugin-{version}")
		}
	}
	library("Micrometer", "1.16.0-M2") {
		considerSnapshots()
		group("io.micrometer") {
			modules = [
				"micrometer-registry-stackdriver" {
					exclude group: "javax.annotation", module: "javax.annotation-api"
				}
			]
			bom("micrometer-bom")
		}
		links {
			site("https://micrometer.io")
			javadoc("micrometer-core", version -> "https://javadoc.io/doc/io.micrometer/micrometer-core/%s".formatted(version), "io.micrometer.core")
			javadoc("micrometer-observation", version -> "https://javadoc.io/doc/io.micrometer/micrometer-observation/%s".formatted(version), "io.micrometer.observation")
			javadoc("micrometer-registry-graphite", version -> "https://javadoc.io/doc/io.micrometer/micrometer-registry-graphite/%s".formatted(version), "io.micrometer.graphite")
			javadoc("micrometer-registry-jmx", version -> "https://javadoc.io/doc/io.micrometer/micrometer-registry-jmx/%s".formatted(version), "io.micrometer.jmx")
			javadoc("micrometer-new-relic", version -> "https://javadoc.io/doc/io.micrometer/micrometer-registry-new-relic/%s".formatted(version), "io.micrometer.newrelic")
			docs(version -> "https://docs.micrometer.io/micrometer/reference/%s.%s"
				.formatted(version.major(), version.minor()))
			releaseNotes("https://github.com/micrometer-metrics/micrometer/releases/tag/v{version}")
		}
	}
	library("Micrometer Tracing", "1.6.0-M2") {
		considerSnapshots()
		group("io.micrometer") {
			bom("micrometer-tracing-bom")
		}
		links {
			site("https://micrometer.io")
			javadoc("https://javadoc.io/doc/io.micrometer/micrometer-tracing/{version}", "io.micrometer.tracing")
			docs(version -> "https://docs.micrometer.io/tracing/reference/%s.%s"
				.formatted(version.major(), version.minor()))
			releaseNotes("https://github.com/micrometer-metrics/tracing/releases/tag/v{version}")
		}
	}
	library("Mockito", "${mockitoVersion}") {
		group("org.mockito") {
			bom("mockito-bom")
		}
		links {
			site("https://site.mockito.org")
			releaseNotes("https://github.com/mockito/mockito/releases/tag/v{version}")
		}
	}
	library("MongoDB", "5.5.1") {
		alignWith {
			version {
				of "org.mongodb:mongodb-driver-core"
				from "org.springframework.data:spring-data-mongodb"
				managedBy "Spring Data Bom"
			}
		}
		group("org.mongodb") {
			bom("mongodb-driver-bom")
		}
		links {
			site("https://github.com/mongodb/mongo-java-driver")
			// Mongo has split packages so we can't use them
			javadoc("mongodb-driver-core", version -> "https://mongodb.github.io/mongo-java-driver/%s.%s/apidocs/mongodb-driver-core".formatted(version.major(), version.minor()))
			javadoc("mongodb-driver-sync", version -> "https://mongodb.github.io/mongo-java-driver/%s.%s/apidocs/mongodb-driver-sync".formatted(version.major(), version.minor()))
			releaseNotes("https://github.com/mongodb/mongo-java-driver/releases/tag/r{version}")
		}
	}
	library("MSSQL JDBC", "12.10.1.jre11") {
		prohibit {
			endsWith(".jre8")
			because "we want to use the jre11 version"
		}
		prohibit {
			endsWith("-preview")
			because "we only want to use non-preview releases"
		}
		group("com.microsoft.sqlserver") {
			modules = [
				"mssql-jdbc"
			]
		}
		links {
			site("https://github.com/microsoft/mssql-jdbc")
			releaseNotes(version -> "https://github.com/microsoft/mssql-jdbc/releases/tag/v%s"
				.formatted(version.toString().replace(".jre11", "")))
		}
	}
	library("MySQL", "9.4.0") {
		upgradePolicy = "same-major-version"
		group("com.mysql") {
			modules = [
				"mysql-connector-j" {
					exclude group: "com.google.protobuf", module: "protobuf-java"
				}
			]
		}
		links {
			releaseNotes(version -> "https://dev.mysql.com/doc/relnotes/connector-j/en/news-%s.html"
				.formatted(version.toString().replace(".", "-")))
		}
	}
	library("Native Build Tools Plugin", "${nativeBuildToolsVersion}") {
		group("org.graalvm.buildtools") {
			plugins = [
					"native-maven-plugin"
			]
		}
		links {
			site("https://github.com/graalvm/native-build-tools")
			releaseNotes("https://github.com/graalvm/native-build-tools/releases/tag/{version}")
		}
	}
	library("NekoHTML", "1.9.22") {
		group("net.sourceforge.nekohtml") {
			modules = [
				"nekohtml"
			]
		}
	}
	library("Neo4j Java Driver", "5.28.9") {
		alignWith {
			version {
				from "org.springframework.data:spring-data-neo4j"
				managedBy "Spring Data Bom"
			}
		}
		group("org.neo4j.driver") {
			modules = [
				"neo4j-java-driver"
			]
		}
		links {
			site("https://github.com/neo4j/neo4j-java-driver")
			javadoc("https://javadoc.io/doc/org.neo4j.driver/neo4j-java-driver/{version}", "org.neo4j.driver")
			releaseNotes("https://github.com/neo4j/neo4j-java-driver/releases/tag/{version}")
		}
	}
	library("Netty", "4.2.4.Final") {
		prohibit {
			contains ".Alpha"
			contains ".Beta"
			contains ".RC"
			because "we don't want alphas, betas, or release candidates"
		}
		group("io.netty") {
			bom("netty-bom")
		}
		links {
			site("https://netty.io")
			javadoc(version -> "https://netty.io/%s.%s/api".formatted(version.major(), version.minor()), "io.netty")
		}
	}
	library("OpenTelemetry", "1.53.0") {
		group("io.opentelemetry") {
			bom("opentelemetry-bom")
		}
		links {
			site("https://github.com/open-telemetry/opentelemetry-java")
			javadoc("opentelemetry-api", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-api/%s".formatted(version), "io.opentelemetry.api")
			javadoc("opentelemetry-context", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-context/%s".formatted(version), "io.opentelemetry.context")
			javadoc("opentelemetry-sdk-common", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-sdk-common/%s".formatted(version), "io.opentelemetry.sdk.common", "io.opentelemetry.sdk.resources")
			javadoc("opentelemetry-sdk-logs", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-sdk-logs/%s".formatted(version), "io.opentelemetry.sdk.logs")
			javadoc("opentelemetry-sdk-metrics", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-sdk-metrics/%s".formatted(version), "io.opentelemetry.sdk.metrics")
			javadoc("opentelemetry-sdk-trace", version -> "https://javadoc.io/doc/io.opentelemetry/opentelemetry-sdk-trace/%s".formatted(version), "io.opentelemetry.sdk.trace")
			releaseNotes("https://github.com/open-telemetry/opentelemetry-java/releases/tag/v{version}")
		}
	}
	library("Oracle Database", "23.9.0.25.07") {
		alignWith {
			dependencyManagementDeclaredIn("com.oracle.database.jdbc:ojdbc-bom")
		}
		group("com.oracle.database.ha") {
			modules = [
				"ons",
				"simplefan"
			]
		}
		group("com.oracle.database.jdbc") {
			modules = [
				"ojdbc11",
				"ojdbc11-production",
				"ojdbc17",
				"ojdbc17-production",
				"ojdbc8",
				"ojdbc8-production",
				"rsi",
				"ucp",
				"ucp11",
				"ucp17"
			]
		}
		group("com.oracle.database.nls") {
			modules = [
				"orai18n"
			]
		}
		group("com.oracle.database.security") {
			modules = [
				"oraclepki"
			]
		}
		group("com.oracle.database.xml") {
			modules = [
				"xdb",
				"xmlparserv2"
			]
		}
	}
	library("Oracle R2DBC", "1.3.0") {
		group("com.oracle.database.r2dbc") {
			modules = [
				"oracle-r2dbc"
			]
		}
		links {
			releaseNotes("https://github.com/oracle/oracle-r2dbc/releases/tag/{version}")
		}
	}
	library("Pooled JMS", "3.1.7") {
		group("org.messaginghub") {
			modules = [
				"pooled-jms"
			]
		}
		links {
			javadoc("https://javadoc.io/doc/org.messaginghub/pooled-jms/{version}", "org.messaginghub.pooled.jms")
		}
	}
	library("Postgresql", "42.7.7") {
		group("org.postgresql") {
			modules = [
				"postgresql"
			]
		}
		links {
			site("https://github.com/pgjdbc/pgjdbc")
			javadoc("https://jdbc.postgresql.org/documentation/publicapi", "org.postgresql")
			releaseNotes("https://github.com/pgjdbc/pgjdbc/releases/tag/REL{version}")
		}
	}
	library("Prometheus Client", "1.3.10") {
		group("io.prometheus") {
			bom("prometheus-metrics-bom")
		}
		links {
			site("https://github.com/prometheus/client_java")
			javadoc("prometheus-metrics-tracer-common", (version) -> "https://javadoc.io/doc/io.prometheus/prometheus-metrics-tracer-common/%s".formatted(version), "io.prometheus.metrics.tracer.common")
			releaseNotes("https://github.com/prometheus/client_java/releases/tag/v{version}")
		}
	}
	library("Prometheus Simpleclient", "0.16.0") {
		group("io.prometheus") {
			bom("simpleclient_bom")
		}
		links {
			site("https://github.com/prometheus/client_java")
			javadoc("prometheus-simpleclient-tracer-common", (version) -> "https://javadoc.io/doc/io.prometheus/simpleclient_tracer_common/%s".formatted(version), "io.prometheus.client.exemplars.tracer.common")
			releaseNotes("https://github.com/prometheus/client_java/releases/tag/parent-{version}")
		}
	}
	library("Pulsar", "4.0.6") {
		group("org.apache.pulsar") {
			bom("pulsar-bom") {
				permit("org.apache.maven.plugin-tools:maven-plugin-annotations")
			}
		}
		links {
			site("https://pulsar.apache.org")
			docs(version -> "https://pulsar.apache.org/docs/%s.%s.x"
				.formatted(version.major(), version.minor()))
			releaseNotes("https://pulsar.apache.org/release-notes/versioned/pulsar-{version}")
		}
	}
	library("Pulsar Reactive", "0.7.0") {
		group("org.apache.pulsar") {
			bom("pulsar-client-reactive-bom")
		}
		links {
			site("https://github.com/apache/pulsar-client-reactive")
			releaseNotes("https://github.com/apache/pulsar-client-reactive/releases/tag/v{version}")
		}
	}
	library("Quartz", "2.5.0") {
		group("org.quartz-scheduler") {
			modules = [
				"quartz",
				"quartz-jobs"
			]
		}
		links {
			site("https://github.com/quartz-scheduler/quartz")
			javadoc("https://javadoc.io/doc/org.quartz-scheduler/quartz/{version}", "org.quartz")
			releaseNotes("https://github.com/quartz-scheduler/quartz/releases/tag/v{version}")
		}
	}
	library("QueryDSL", "5.1.0") {
		group("com.querydsl") {
			bom("querydsl-bom")
		}
		links {
			site("https://github.com/querydsl/querydsl")
			releaseNotes(version -> "https://github.com/querydsl/querydsl/releases/tag/QUERYDSL_%s"
				.formatted(version.toString("_")))
		}
	}
	library("R2DBC H2", "1.0.0.RELEASE") {
		considerSnapshots()
		group("io.r2dbc") {
			modules = [
				"r2dbc-h2"
			]
		}
		links {
			releaseNotes("https://github.com/r2dbc/r2dbc-h2/releases/tag/v{version}")
		}
	}
	library("R2DBC MariaDB", "1.3.0") {
		group("org.mariadb") {
			modules = [
				"r2dbc-mariadb"
			]
		}
		links {
			releaseNotes("https://github.com/mariadb-corporation/mariadb-connector-r2dbc/releases/tag/{version}")
		}
	}
	library("R2DBC MSSQL", "1.0.2.RELEASE") {
		group ("io.r2dbc") {
			modules = [
				"r2dbc-mssql"
			]
		}
		links {
			releaseNotes("https://github.com/r2dbc/r2dbc-mssql/releases/tag/v{version}")
		}
	}
	library("R2DBC MySQL", "1.4.1") {
		group("io.asyncer") {
			modules = [
				"r2dbc-mysql"
			]
		}
		links {
			releaseNotes("https://github.com/asyncer-io/r2dbc-mysql/releases/tag/r2dbc-mysql-{version}")
		}
	}
	library("R2DBC Pool", "1.0.2.RELEASE") {
		considerSnapshots()
		group("io.r2dbc") {
			modules = [
				"r2dbc-pool"
			]
		}
		links {
			site("https://github.com/r2dbc/r2dbc-pool")
			releaseNotes("https://github.com/r2dbc/r2dbc-pool/releases/tag/v{version}")
		}
	}
	library("R2DBC Postgresql", "1.0.7.RELEASE") {
		considerSnapshots()
		group("org.postgresql") {
			modules = [
				"r2dbc-postgresql"
			]
		}
		links {
			releaseNotes("https://github.com/pgjdbc/r2dbc-postgresql/releases/tag/v{version}")
		}
	}
	library("R2DBC Proxy", "1.1.6.RELEASE") {
		considerSnapshots()
		group("io.r2dbc") {
			modules = [
				"r2dbc-proxy"
			]
		}
		links {
			releaseNotes("https://github.com/r2dbc/r2dbc-proxy/releases/tag/v{version}")
		}
	}
	library("R2DBC SPI", "1.0.0.RELEASE") {
		considerSnapshots()
		group("io.r2dbc") {
			modules = [
				"r2dbc-spi"
			]
		}
		links {
			site("https://r2dbc.io")
			javadoc("https://r2dbc.io/spec/{version}/api", "io.r2dbc")
			releaseNotes("https://github.com/r2dbc/r2dbc-spi/releases/tag/v{version}")
		}
	}
	library("Rabbit AMQP Client", "5.26.0") {
		group("com.rabbitmq") {
			modules = [
				"amqp-client"
			]
		}
		links {
			site("https://github.com/rabbitmq/rabbitmq-java-client")
			javadoc("https://rabbitmq.github.io/rabbitmq-java-client/api/current", "com.rabbitmq")
			releaseNotes("https://github.com/rabbitmq/rabbitmq-java-client/releases/tag/v{version}")
		}
	}
	library("Rabbit Stream Client", "0.23.0") {
		prohibit {
			versionRange "[0.24.0,)"
			because "It requires Netty 4.2.0"
		}
		group("com.rabbitmq") {
			modules = [
					"stream-client"
			]
		}
		links {
			site("https://github.com/rabbitmq/rabbitmq-stream-java-client")
			releaseNotes("https://github.com/rabbitmq/rabbitmq-stream-java-client/releases/tag/v{version}")
		}
	}
	library("Reactive Streams", "1.0.4") {
		group("org.reactivestreams") {
			modules = [
				"reactive-streams"
			]
		}
	}
	library("Reactor Bom", "2025.0.0-M6") {
		considerSnapshots()
		calendarName = "Reactor"
		group("io.projectreactor") {
			bom("reactor-bom")
		}
		links {
			site("https://projectreactor.io")
			releaseNotes("https://github.com/reactor/reactor/releases/tag/{version}")
		}
	}
	library("REST Assured", "5.5.5") {
		group("io.rest-assured") {
			bom("rest-assured-bom")
		}
		links {
			javadoc("https://javadoc.io/doc/io.rest-assured/rest-assured/{version}", "io.restassured")
		}
	}
	library("RSocket", "1.1.5") {
		group("io.rsocket") {
			bom("rsocket-bom")
		}
		links {
			site("https://github.com/rsocket/rsocket-java")
			javadoc("https://javadoc.io/doc/io.rsocket/rsocket-core/{version}", "io.rsocket")
			releaseNotes("https://github.com/rsocket/rsocket-java/releases/tag/{version}")
		}
	}
	library("RxJava3", "3.1.11") {
		group("io.reactivex.rxjava3") {
			modules = [
				"rxjava"
			]
		}
		links {
			releaseNotes("https://github.com/ReactiveX/RxJava/releases/tag/v{version}")
		}
	}
	library("Spring Boot", "${version}") {
		group("org.springframework.boot") {
			modules = [
				"spring-boot",
				"spring-boot-activemq",
				"spring-boot-actuator",
				"spring-boot-actuator-autoconfigure",
				"spring-boot-amqp",
				"spring-boot-artemis",
				"spring-boot-autoconfigure",
				"spring-boot-autoconfigure-classic",
				"spring-boot-autoconfigure-classic-modules",
				"spring-boot-autoconfigure-processor",
				"spring-boot-batch",
				"spring-boot-buildpack-platform",
				"spring-boot-cache",
				"spring-boot-cassandra",
				"spring-boot-cloudfoundry",
				"spring-boot-configuration-metadata",
				"spring-boot-configuration-processor",
				"spring-boot-couchbase",
				"spring-boot-data-cassandra",
				"spring-boot-data-commons",
				"spring-boot-data-couchbase",
				"spring-boot-data-elasticsearch",
				"spring-boot-data-jdbc",
				"spring-boot-data-jpa",
				"spring-boot-data-ldap",
				"spring-boot-data-mongodb",
				"spring-boot-data-neo4j",
				"spring-boot-data-r2dbc",
				"spring-boot-data-redis",
				"spring-boot-data-rest",
				"spring-boot-devtools",
				"spring-boot-docker-compose",
				"spring-boot-elasticsearch",
				"spring-boot-flyway",
				"spring-boot-freemarker",
				"spring-boot-graphql",
				"spring-boot-graphql-test",
				"spring-boot-groovy-templates",
				"spring-boot-gson",
				"spring-boot-h2console",
				"spring-boot-hateoas",
				"spring-boot-hazelcast",
				"spring-boot-health",
				"spring-boot-hibernate",
				"spring-boot-http-client",
				"spring-boot-http-codec",
				"spring-boot-http-converter",
				"spring-boot-integration",
				"spring-boot-jackson",
				"spring-boot-jarmode-tools",
				"spring-boot-jdbc",
				"spring-boot-jersey",
				"spring-boot-jetty",
				"spring-boot-jms",
				"spring-boot-jooq",
				"spring-boot-jpa",
				"spring-boot-jsonb",
				"spring-boot-kafka",
				"spring-boot-ldap",
				"spring-boot-liquibase",
				"spring-boot-loader",
				"spring-boot-loader-classic",
				"spring-boot-loader-tools",
				"spring-boot-mail",
				"spring-boot-metrics",
				"spring-boot-mongodb",
				"spring-boot-mustache",
				"spring-boot-neo4j",
				"spring-boot-netty",
				"spring-boot-observation",
				"spring-boot-opentelemetry",
				"spring-boot-properties-migrator",
				"spring-boot-pulsar",
				"spring-boot-quartz",
				"spring-boot-r2dbc",
				"spring-boot-reactor",
				"spring-boot-reactor-netty",
				"spring-boot-restclient",
				"spring-boot-restclient-test",
				"spring-boot-rsocket",
				"spring-boot-security",
				"spring-boot-security-oauth2-authorization-server",
				"spring-boot-security-oauth2-client",
				"spring-boot-security-oauth2-resource-server",
				"spring-boot-security-saml2",
				"spring-boot-sendgrid",
				"spring-boot-servlet",
				"spring-boot-session",
				"spring-boot-session-data-mongodb",
				"spring-boot-session-data-redis",
				"spring-boot-session-hazelcast",
				"spring-boot-session-jdbc",
				"spring-boot-sql",
				"spring-boot-starter",
				"spring-boot-starter-activemq",
				"spring-boot-starter-actuator",
				"spring-boot-starter-amqp",
				"spring-boot-starter-aop",
				"spring-boot-starter-artemis",
				"spring-boot-starter-batch",
				"spring-boot-starter-cassandra",
				"spring-boot-starter-cache",
				"spring-boot-starter-classic",
				"spring-boot-starter-cloudfoundry",
				"spring-boot-starter-couchbase",
				"spring-boot-starter-data-cassandra",
				"spring-boot-starter-data-cassandra-reactive",
				"spring-boot-starter-data-couchbase",
				"spring-boot-starter-data-couchbase-reactive",
				"spring-boot-starter-data-elasticsearch",
				"spring-boot-starter-data-jdbc",
				"spring-boot-starter-data-jpa",
				"spring-boot-starter-data-ldap",
				"spring-boot-starter-data-mongodb",
				"spring-boot-starter-data-mongodb-reactive",
				"spring-boot-starter-data-neo4j",
				"spring-boot-starter-data-r2dbc",
				"spring-boot-starter-data-redis",
				"spring-boot-starter-data-redis-reactive",
				"spring-boot-starter-data-rest",
				"spring-boot-starter-elasticsearch",
				"spring-boot-starter-flyway",
				"spring-boot-starter-freemarker",
				"spring-boot-starter-graphql",
				"spring-boot-starter-groovy-templates",
				"spring-boot-starter-gson",
				"spring-boot-starter-hateoas",
				"spring-boot-starter-hazelcast",
				"spring-boot-starter-integration",
				"spring-boot-starter-jackson",
				"spring-boot-starter-jdbc",
				"spring-boot-starter-jersey",
				"spring-boot-starter-jetty",
				"spring-boot-starter-jms",
				"spring-boot-starter-jooq",
				"spring-boot-starter-json",
				"spring-boot-starter-jsonb",
				"spring-boot-starter-kafka",
				"spring-boot-starter-ldap",
				"spring-boot-starter-liquibase",
				"spring-boot-starter-log4j2",
				"spring-boot-starter-logback",
				"spring-boot-starter-logging",
				"spring-boot-starter-mail",
				"spring-boot-starter-mongodb",
				"spring-boot-starter-mustache",
				"spring-boot-starter-neo4j",
				"spring-boot-starter-oauth2-authorization-server",
				"spring-boot-starter-oauth2-client",
				"spring-boot-starter-oauth2-resource-server",
				"spring-boot-starter-opentelemetry",
				"spring-boot-starter-pulsar",
				"spring-boot-starter-pulsar-reactive",
				"spring-boot-starter-quartz",
				"spring-boot-starter-r2dbc",
				"spring-boot-starter-reactor",
				"spring-boot-starter-reactor-netty",
				"spring-boot-starter-restclient",
				"spring-boot-starter-rsocket",
				"spring-boot-starter-security",
				"spring-boot-starter-security-oauth2-authorization-server",
				"spring-boot-starter-security-oauth2-client",
				"spring-boot-starter-security-oauth2-resource-server",
				"spring-boot-starter-security-saml2",
				"spring-boot-starter-sendgrid",
				"spring-boot-starter-session-data-mongodb",
				"spring-boot-starter-session-data-redis",
				"spring-boot-starter-session-hazelcast",
				"spring-boot-starter-session-jdbc",
				"spring-boot-starter-sql",
				"spring-boot-starter-test",
				"spring-boot-starter-thymeleaf",
				"spring-boot-starter-tomcat",
				"spring-boot-starter-undertow",
				"spring-boot-starter-validation",
				"spring-boot-starter-web",
				"spring-boot-starter-web-services",
				"spring-boot-starter-webclient",
				"spring-boot-starter-webflux",
				"spring-boot-starter-webmvc",
				"spring-boot-starter-webservices",
				"spring-boot-starter-websocket",
				"spring-boot-starter-zipkin",
				"spring-boot-test",
				"spring-boot-test-autoconfigure",
				"spring-boot-testcontainers",
				"spring-boot-thymeleaf",
				"spring-boot-tomcat",
				"spring-boot-tracing",
				"spring-boot-tx",
				"spring-boot-undertow",
				"spring-boot-validation",
				"spring-boot-webclient",
				"spring-boot-webflux",
				"spring-boot-webmvc",
				"spring-boot-webservices",
				"spring-boot-websocket",
				"spring-boot-web-server",
				"spring-boot-web-server-test",
				"spring-boot-zipkin"
			]
			plugins = [
				"spring-boot-maven-plugin"
			]
		}
		links {
			site("https://spring.io/projects/spring-boot")
			github("https://github.com/spring-projects/spring-boot")
			javadoc("https://docs.spring.io/spring-boot/{version}/api/java", "org.springframework.boot")
			docs("https://docs.spring.io/spring-boot/{version}")
			releaseNotes("https://github.com/spring-projects/spring-boot/releases/tag/v{version}")
			add("layers-xsd", version -> "https://www.springframework.org/schema/boot/layers/layers-%s.%s.xsd"
				.formatted(version.major(), version.minor()))
		}
	}
	library("SAAJ Impl", "3.0.4") {
		group("com.sun.xml.messaging.saaj") {
			modules = [
				"saaj-impl"
			]
		}
	}
	library("Selenium", "4.34.0") {
		group("org.seleniumhq.selenium") {
			bom("selenium-bom")
		}
		links {
			site("https://www.selenium.dev")
			javadoc("https://www.selenium.dev/selenium/docs/api/java", "org.openqa.selenium")
			releaseNotes("https://github.com/SeleniumHQ/selenium/releases/tag/selenium-{version}")
		}
	}
	library("Selenium HtmlUnit", "4.34.0") {
		group("org.seleniumhq.selenium") {
			modules = [
				"htmlunit3-driver"
			]
		}
		links {
			site("https://github.com/SeleniumHQ/htmlunit-driver")
			releaseNotes("https://github.com/SeleniumHQ/htmlunit-driver/releases/tag/{version}")
		}
	}
	library("SendGrid", "4.10.3") {
		prohibit {
			contains "-rc."
			because "we don't want release candidates"
		}
		group("com.sendgrid") {
			modules = [
				"sendgrid-java"
			]
		}
		links {
			site("https://github.com/sendgrid/sendgrid-java")
			releaseNotes("https://github.com/sendgrid/sendgrid-java/releases/tag/{version}")
		}
	}
	library("SLF4J", "2.0.17") {
		prohibit {
			contains "-alpha"
			because "we don't want alphas"
		}
		group("org.slf4j") {
			modules = [
				"jcl-over-slf4j",
				"jul-to-slf4j",
				"log4j-over-slf4j",
				"slf4j-api",
				"slf4j-ext",
				"slf4j-jdk-platform-logging",
				"slf4j-jdk14",
				"slf4j-log4j12",
				"slf4j-nop",
				"slf4j-reload4j",
				"slf4j-simple"
			]
		}
	}
	library("SnakeYAML", "${snakeYamlVersion}") {
		group("org.yaml") {
			modules = [
				"snakeyaml"
			]
		}
	}
	library("Spring AMQP", "4.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.amqp") {
			bom("spring-amqp-bom")
		}
		links {
			site("https://spring.io/projects/spring-amqp")
			github("https://github.com/spring-projects/spring-amqp")
			javadoc(version -> "https://docs.spring.io/spring-amqp/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.amqp", "org.springframework.rabbit")
			docs(version -> "https://docs.spring.io/spring-amqp/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-amqp/releases/tag/v{version}")
		}
	}
	library("Spring Authorization Server", "2.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.security") {
			modules = [
					"spring-security-oauth2-authorization-server"
			]
		}
		links {
			site("https://spring.io/projects/spring-authorization-server")
			github("https://github.com/spring-projects/spring-authorization-server")
			javadoc(version -> "https://docs.spring.io/spring-authorization-server/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.security.oauth2.server")
			docs(version -> "https://docs.spring.io/spring-authorization-server/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-authorization-server/releases/tag/{version}")
		}
	}
	library("Spring Batch", "6.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.batch") {
			bom("spring-batch-bom")
		}
		links {
			site("https://spring.io/projects/spring-batch")
			github("https://github.com/spring-projects/spring-batch")
			javadoc(version -> "https://docs.spring.io/spring-batch/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.batch")
			docs(version -> "https://docs.spring.io/spring-batch/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-batch/releases/tag/v{version}")
		}
	}
	library("Spring Data Bom", "2025.1.0-SNAPSHOT") {
		considerSnapshots()
		calendarName = "Spring Data Release"
		group("org.springframework.data") {
			bom("spring-data-bom")
		}
		links("spring-data") {
			site("https://spring.io/projects/spring-data")
			github("https://github.com/spring-projects/spring-data-bom")
			releaseNotes("https://github.com/spring-projects/spring-data-bom/releases/tag/{version}")
		}
	}
	library("Spring Framework", "${springFrameworkVersion}") {
		considerSnapshots()
		group("org.springframework") {
			bom("spring-framework-bom")
		}
		links {
			site("https://spring.io/projects/spring-framework")
			github("https://github.com/spring-projects/spring-framework")
			javadoc(version -> "https://docs.spring.io/spring-framework/docs/%s/javadoc-api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.[aop|aot|asm|beans|cache|cglib|" +
						"context|core|dao|ejb|expression|format|http|instrument|jca|jdbc|jms|jmx|jndi|lang|mail|" +
						"messaging|mock|objenesis|orm|oxm|r2dbc|scheduling|scripting|stereotype|test|transaction|" +
						"ui|util|validation|web]")
			docs(version -> "https://docs.spring.io/spring-framework/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-framework/releases/tag/v{version}")
		}
	}
	library("Spring GraphQL", "2.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.graphql") {
			modules = [
					"spring-graphql",
					"spring-graphql-test"
			]
		}
		links {
			site("https://spring.io/projects/spring-graphql")
			github("https://github.com/spring-projects/spring-graphql")
			javadoc(version -> "https://docs.spring.io/spring-graphql/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.graphql")
			docs(version -> "https://docs.spring.io/spring-graphql/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-graphql/releases/tag/v{version}")
		}
	}
	library("Spring HATEOAS", "3.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.hateoas") {
			modules = [
				"spring-hateoas"
			]
		}
		links {
			site("https://spring.io/projects/spring-hateoas")
			github("https://github.com/spring-projects/spring-hateoas")
			javadoc(version -> "https://docs.spring.io/spring-hateoas/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.hateoas")
			docs(version -> "https://docs.spring.io/spring-hateoas/docs/%s/reference/html"
				.formatted(version.forMajorMinorGeneration()))
			releaseNotes("https://github.com/spring-projects/spring-hateoas/releases/tag/{version}")
		}
	}
	library("Spring Integration", "7.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.integration") {
			bom("spring-integration-bom")
		}
		links {
			site("https://spring.io/projects/spring-integration")
			github("https://github.com/spring-projects/spring-integration")
			javadoc(version -> "https://docs.spring.io/spring-integration/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.integration")
			docs(version -> "https://docs.spring.io/spring-integration/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-integration/releases/tag/v{version}")
		}
	}
	library("Spring Kafka", "4.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.kafka") {
			modules = [
				"spring-kafka",
				"spring-kafka-test"
			]
		}
		links {
			site("https://spring.io/projects/spring-kafka")
			github("https://github.com/spring-projects/spring-kafka")
			javadoc(version -> "https://docs.spring.io/spring-kafka/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.kafka")
			docs(version -> "https://docs.spring.io/spring-kafka/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-kafka/releases/tag/v{version}")
		}
	}
	library("Spring LDAP", "4.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.ldap") {
			modules = [
				"spring-ldap-core",
				"spring-ldap-ldif-core",
				"spring-ldap-odm",
				"spring-ldap-test"
			]
		}
		links {
			site("https://spring.io/projects/spring-ldap")
			github("https://github.com/spring-projects/spring-ldap")
			javadoc(version -> "https://docs.spring.io/spring-ldap/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.ldap")
			docs(version -> "https://docs.spring.io/spring-ldap/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-ldap/releases/tag/{version}")
		}
	}
	library("Spring Pulsar", "2.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.pulsar") {
			bom("spring-pulsar-bom")
		}
		links {
			site("https://spring.io/projects/spring-pulsar")
			github("https://github.com/spring-projects/spring-pulsar")
			javadoc(version -> "https://docs.spring.io/spring-pulsar/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.pulsar")
			docs(version -> "https://docs.spring.io/spring-pulsar/docs/%s/reference"
				.formatted(version.forMajorMinorGeneration()))
			releaseNotes("https://github.com/spring-projects/spring-pulsar/releases/tag/v{version}")
		}
	}
	library("Spring RESTDocs", "4.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.restdocs") {
			bom("spring-restdocs-bom")
		}
		links {
			site("https://spring.io/projects/spring-restdocs")
			github("https://github.com/spring-projects/spring-restdocs")
			javadoc(version -> "https://docs.spring.io/spring-restdocs/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.restdocs")
			docs(version -> "https://docs.spring.io/spring-restdocs/docs/%s/reference/htmlsingle"
					.formatted(version.forMajorMinorGeneration()))
			releaseNotes("https://github.com/spring-projects/spring-restdocs/releases/tag/v{version}")
		}
	}
	library("Spring Retry", "2.0.12") {
		considerSnapshots()
		group("org.springframework.retry") {
			modules = [
				"spring-retry"
			]
		}
		links {
			site("https://github.com/spring-projects/spring-retry")
			javadoc("https://docs.spring.io/spring-retry/docs/{version}/apidocs", "org.springframework.retry")
			releaseNotes("https://github.com/spring-projects/spring-retry/releases/tag/v{version}")
		}
	}
	library("Spring Security", "7.0.0-SNAPSHOT") {
		considerSnapshots()
		group("org.springframework.security") {
			bom("spring-security-bom")
		}
		links {
			site("https://spring.io/projects/spring-security")
			github("https://github.com/spring-projects/spring-security")
			javadoc(version -> "https://docs.spring.io/spring-security/site/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.security")
			docs(version -> "https://docs.spring.io/spring-security/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-security/releases/tag/{version}")
		}
	}
	library("Spring Session", "4.0.0-M1") {
		considerSnapshots()
		prohibit {
			startsWith(["Apple-", "Bean-", "Corn-", "Dragonfruit-"])
			because "Spring Session switched to numeric version numbers"
		}
		prohibit {
			versionRange "[2020.0.0-M1,)"
			because "Spring Session stopped using calver"
		}
		group("org.springframework.session") {
			bom("spring-session-bom")
		}
		links {
			site("https://spring.io/projects/spring-session")
			github("https://github.com/spring-projects/spring-session")
			javadoc(version -> "https://docs.spring.io/spring-session/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.session")
			docs(version -> "https://docs.spring.io/spring-session/reference/%s"
				.formatted(version.forAntora()))
			releaseNotes("https://github.com/spring-projects/spring-session/releases/tag/{version}")
		}
	}
	library("Spring WS", "5.0.0-M1") {
		considerSnapshots()
		group("org.springframework.ws") {
			bom("spring-ws-bom")
		}
		links("spring-webservices") {
			site("https://spring.io/projects/spring-ws")
			github("https://github.com/spring-projects/spring-ws")
			javadoc(version -> "https://docs.spring.io/spring-ws/docs/%s/api"
				.formatted(version.forMajorMinorGeneration()), "org.springframework.ws", "org.springframework.xml")
			docs(version -> "https://docs.spring.io/spring-ws/docs/%s/reference/html"
				.formatted(version.forMajorMinorGeneration()))
			releaseNotes("https://github.com/spring-projects/spring-ws/releases/tag/v{version}")
		}
	}
	library("SQLite JDBC", "3.50.3.0") {
		group("org.xerial") {
			modules = [
				"sqlite-jdbc"
			]
		}
		links {
			site("https://github.com/xerial/sqlite-jdbc")
			releaseNotes("https://github.com/xerial/sqlite-jdbc/releases/tag/{version}")
		}
	}
	library("Testcontainers", "1.21.3") {
		group("org.testcontainers") {
			bom("testcontainers-bom")
		}
		links {
			docs("https://java.testcontainers.org")
			javadoc("testcontainers", version -> "https://javadoc.io/doc/org.testcontainers/testcontainers/%s".formatted(version), "org.testcontainers")
			javadoc("testcontainers-activemq", version -> "https://javadoc.io/doc/org.testcontainers/activemq/%s".formatted(version), "org.testcontainers.activemq")
			javadoc("testcontainers-cassandra", version -> "https://javadoc.io/doc/org.testcontainers/cassandra/%s".formatted(version))
			javadoc("testcontainers-clickhouse", version -> "https://javadoc.io/doc/org.testcontainers/clickhouse/%s".formatted(version), "org.testcontainers.clickhouse")
			javadoc("testcontainers-couchbase", version -> "https://javadoc.io/doc/org.testcontainers/couchbase/%s".formatted(version), "org.testcontainers.couchbase")
			javadoc("testcontainers-elasticsearch", version -> "https://javadoc.io/doc/org.testcontainers/elasticsearch/%s".formatted(version), "org.testcontainers.elasticsearch")
			javadoc("testcontainers-grafana", version -> "https://javadoc.io/doc/org.testcontainers/grafana/%s".formatted(version), "org.testcontainers.grafana")
			javadoc("testcontainers-jdbc", version -> "https://javadoc.io/doc/org.testcontainers/jdbc/%s".formatted(version))
			javadoc("testcontainers-junit-jupiter", version -> "https://javadoc.io/doc/org.testcontainers/junit-jupiter/%s".formatted(version), "org.testcontainers.junit.jupiter")
			javadoc("testcontainers-kafka", version -> "https://javadoc.io/doc/org.testcontainers/kafka/%s".formatted(version), "org.testcontainers.kafka")
			javadoc("testcontainers-mariadb", version -> "https://javadoc.io/doc/org.testcontainers/mariadb/%s".formatted(version))
			javadoc("testcontainers-mongodb", version -> "https://javadoc.io/doc/org.testcontainers/mongodb/%s".formatted(version))
			javadoc("testcontainers-mssqlserver", version -> "https://javadoc.io/doc/org.testcontainers/mssqlserver/%s".formatted(version))
			javadoc("testcontainers-mysql", version -> "https://javadoc.io/doc/org.testcontainers/mysql/%s".formatted(version))
			javadoc("testcontainers-neo4j", version -> "https://javadoc.io/doc/org.testcontainers/neo4j/%s".formatted(version))
			javadoc("testcontainers-oracle-xe", version -> "https://javadoc.io/doc/org.testcontainers/oracle-xe/%s".formatted(version))
			javadoc("testcontainers-oracle-free", version -> "https://javadoc.io/doc/org.testcontainers/oracle-free/%s".formatted(version), "org.testcontainers.oracle")
			javadoc("testcontainers-postgresql", version -> "https://javadoc.io/doc/org.testcontainers/postgresql/%s".formatted(version))
			javadoc("testcontainers-pulsar", version -> "https://javadoc.io/doc/org.testcontainers/pulsar/%s".formatted(version))
			javadoc("testcontainers-rabbitmq", version -> "https://javadoc.io/doc/org.testcontainers/rabbitmq/%s".formatted(version))
			javadoc("testcontainers-redpanda", version -> "https://javadoc.io/doc/org.testcontainers/redpanda/%s".formatted(version), "org.testcontainers.redpanda")
			javadoc("testcontainers-r2dbc", version -> "https://javadoc.io/doc/org.testcontainers/r2dbc/%s".formatted(version), "org.testcontainers.r2dbc")
			releaseNotes("https://github.com/testcontainers/testcontainers-java/releases/tag/{version}")
			site("https://java.testcontainers.org")
		}
	}
	library("Testcontainers Redis Module", "2.2.4") {
		group("com.redis") {
			modules = [
				"testcontainers-redis"
			]
		}
		links {
			site("https://testcontainers.com/modules/redis/")
			javadoc("https://javadoc.io/doc/com.redis/testcontainers-redis/{version}", "com.redis.testcontainers")
		}
	}
	library("Thymeleaf", "3.1.3.RELEASE") {
		group("org.thymeleaf") {
			modules = [
				"thymeleaf",
				"thymeleaf-spring6"
			]
		}
		links {
			site("https://www.thymeleaf.org")
			javadoc("thymeleaf", version -> "https://www.thymeleaf.org/apidocs/thymeleaf/%s".formatted(version), "org.thymeleaf")
			javadoc("thymeleaf-spring6", version -> "https://www.thymeleaf.org/apidocs/thymeleaf-spring6/%s".formatted(version), "org.thymeleaf.spring6")
			releaseNotes("https://github.com/thymeleaf/thymeleaf/releases/tag/thymeleaf-{version}")
		}
	}
	library("Thymeleaf Extras Data Attribute", "2.0.1") {
		group("com.github.mxab.thymeleaf.extras") {
			modules = [
				"thymeleaf-extras-data-attribute"
			]
		}
	}
	library("Thymeleaf Extras SpringSecurity", "3.1.3.RELEASE") {
		group("org.thymeleaf.extras") {
			modules = [
				"thymeleaf-extras-springsecurity6"
			]
		}
	}
	library("Thymeleaf Layout Dialect", "3.4.0") {
		group("nz.net.ultraq.thymeleaf") {
			modules = [
				"thymeleaf-layout-dialect"
			]
		}
		links {
			releaseNotes("https://github.com/ultraq/thymeleaf-layout-dialect/releases/tag/{version}")
		}
	}
	library("Tomcat", "${tomcatVersion}") {
		group("org.apache.tomcat") {
			modules = [
				"tomcat-annotations-api",
				"tomcat-jdbc",
				"tomcat-jsp-api"
			]
		}
		group("org.apache.tomcat.embed") {
			modules = [
				"tomcat-embed-core",
				"tomcat-embed-el",
				"tomcat-embed-jasper",
				"tomcat-embed-websocket"
			]
		}
		links {
			site("https://tomcat.apache.org")
			javadoc(version -> "https://tomcat.apache.org/tomcat-%s.%s-doc/api".formatted(version.major(), version.minor()), "org.apache.catalina", "org.apache.tomcat")
			docs(version -> "https://tomcat.apache.org/tomcat-%s.%s-doc".formatted(version.major(), version.minor()))
			releaseNotes(version -> "https://tomcat.apache.org/tomcat-%s.%s-doc/changelog.html".formatted(version.major(), version.minor()))
		}
	}
	library("UnboundID LDAPSDK", "7.0.3") {
		group("com.unboundid") {
			modules = [
				"unboundid-ldapsdk"
			]
		}
		links {
			releaseNotes("https://github.com/pingidentity/ldapsdk/releases/tag/{version}")
		}
	}
	library("Undertow", "2.3.18.Final") {
		group("io.undertow") {
			modules = [
				"undertow-core",
				"undertow-servlet",
				"undertow-websockets-jsr"
			]
		}
		links {
			releaseNotes("https://github.com/undertow-io/undertow/releases/tag/{version}")
		}
	}
	library("Versions Maven Plugin", "2.18.0") {
		group("org.codehaus.mojo") {
			plugins = [
				"versions-maven-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/mojohaus/versions/releases/tag/{version}")
		}
	}
	library("Vibur", "26.0") {
		group("org.vibur") {
			modules = [
					"vibur-dbcp",
					"vibur-object-pool"
			]
		}
	}
	library("WebJars Locator Core", "0.59") {
		group("org.webjars") {
			modules = [
				"webjars-locator-core"
			]
		}
	}
	library("WebJars Locator Lite", "1.1.0") {
		group("org.webjars") {
			modules = [
				"webjars-locator-lite"
			]
		}
	}
	library("WSDL4j", "1.6.3") {
		group("wsdl4j") {
			modules = [
				"wsdl4j"
			]
		}
	}
	library("XML Maven Plugin", "1.1.0") {
		group("org.codehaus.mojo") {
			plugins = [
				"xml-maven-plugin"
			]
		}
		links {
			releaseNotes("https://github.com/mojohaus/xml-maven-plugin/releases/tag/{version}")
		}
	}
	library("XmlUnit2", "2.10.3") {
		group("org.xmlunit") {
			modules = [
				"xmlunit-assertj",
				"xmlunit-assertj3",
				"xmlunit-core",
				"xmlunit-jakarta-jaxb-impl",
				"xmlunit-legacy",
				"xmlunit-matchers",
				"xmlunit-placeholders"
			]
		}
		links {
			site("https://github.com/xmlunit/xmlunit")
			releaseNotes("https://github.com/xmlunit/xmlunit/releases/tag/v{version}")
		}
	}
	library("Yasson", "3.0.4") {
		group("org.eclipse") {
			modules = [
				"yasson"
			]
		}
		links {
			site("https://github.com/eclipse-ee4j/yasson")
			releaseNotes("https://github.com/eclipse-ee4j/yasson/releases/tag/{version}")
		}
	}
}

generateMetadataFileForMavenPublication {
	enabled = false
}
