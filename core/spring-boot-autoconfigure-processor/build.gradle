/*
 * Copyright 2012-present the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
	id "java-library"
	id "org.springframework.boot.deployed"
	id "org.springframework.boot.annotation-processor"
}

description = "Spring Boot AutoConfigure Annotation Processor"

dependencies {
	testImplementation(enforcedPlatform(project(":platform:spring-boot-dependencies")))
	testImplementation(project(":test-support:spring-boot-test-support"))
}

architectureCheck {
	nullMarked = false
}
